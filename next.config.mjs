/** @type {import('next').NextConfig} */

const nextConfig = {
    output: 'standalone',
    images: {
      domains: ['images.unsplash.com', 'tailwindui.com','localhost','127.0.0.1'],
    },
    typescript: {
      // !! WARN !!
      // Dangerously allow production builds to successfully complete even if
      // your project has type errors.
      // !! WARN !!
      ignoreBuildErrors: true,
    },
  };
  
  export default nextConfig;
  