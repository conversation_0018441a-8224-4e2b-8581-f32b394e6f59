{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext", "es2015"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "baseUrl": "./",
    "resolveJsonModule": true,
    "isolatedModules": true,
    
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*",],
      // "@/components/*": ["components/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/utils/generateImage.js", "src/api/home/<USER>/group/getUsersInGroup.tsx", "src/__tests__/*", "src/__tests__/home/<USER>/moduleIdPage.js", "src/__tests__/home/<USER>/contentpage.js", "tailwind.config.js", "src/components/ui/tabs.js"],
  "exclude": ["node_modules"]
}
