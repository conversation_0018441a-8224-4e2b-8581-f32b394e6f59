{"name": "take-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "jest", "test:watch": "jest --watch", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.16", "@headlessui/tailwindcss": "^0.2.1", "@heroicons/react": "^2.0.18", "@radix-ui/react-label": "^2.1.1", "@react-three/fiber": "^8.17.14", "@tabler/icons-react": "^3.29.0", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "4.32.6", "@tsparticles/engine": "^3.8.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.0", "@types/react": "^18.3.2", "@types/react-dom": "18.2.7", "@types/three": "^0.172.0", "axios": "^1.6.0", "clsx": "^2.1.1", "framer-motion": "^10.15.0", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "motion": "^12.0.11", "next": "^13.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "react-type-animation": "^3.2.0", "simplex-noise": "^4.0.3", "swiper": "^11.2.1", "tailwind-merge": "^3.0.1", "tailwindcss": "3.3.3", "three": "^0.173.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/js-cookie": "^3.0.6", "@types/react-datepicker": "^6.2.0", "autoprefixer": "^10.4.20", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "5.7.3"}}