import unusedImports from 'eslint-plugin-unused-imports';  // This line is essential!

export default [
    {
        files: ["**/*.js", "**/*.cjs", "**/*.mjs"],
        plugins: {
            "unused-imports": unusedImports, // Now it will work
        },
        rules: {
            "prefer-const": "warn",
            "no-constant-binary-expression": "error",
            "unused-imports/no-unused-imports": "error", // Add the rule here
            "unused-imports/no-unused-vars": "warn", // Optional: for unused variables
        },
    },
    // ... other configuration objects if you have them
];