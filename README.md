# HR-MATH

HR-<PERSON><PERSON> leverages AI to streamline the recruitment process by filtering candidates from their CVs based on the provided job descriptions. Our platform ensures that you find the most suitable candidates efficiently and effectively.

## Features

- **AI-Powered Filtering:** Automatically analyze and match candidate CVs with job descriptions.
- **User-Friendly Interface:** Intuitive design for easy navigation and management.
- **Customizable Criteria:** Tailor the filtering parameters to meet specific recruitment needs.
- **Scalable Architecture:** Handle large volumes of data with ease.

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v14 or later)
- [Yarn](https://yarnpkg.com/) package manager

### Installation

1. **Clone the repository:**

   ```bash
   git clone https://github.com/dono-bdec/talentlens-backend
   cd talentlens.ai
   ```

2. **Install the dependencies:**

   ```bash
   yarn install
   ```
3. **To run the local server for development:**

   ```bash
   yarn dev
   ```
   
