import { API_URL } from "@/utils/constants";
import { getUser } from "../api/user.localStorage";
import { removeTimer } from '@/api/timer.localStorage';
import { useRouter } from 'next/router'; // Import Next.js router for client-side redirects

export function logoutAll() {
  const user = getUser();
  removeTimer();

  return fetch(API_URL + "user/logout", {
    method: "GET",
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${user?.token}`,
    },
  })
    .then((res) => {
      if (res.status === 401) {
        // Redirect to login page on unauthorized access
        if (typeof window !== "undefined") {
          window.location.href = '/login'; // Fallback for non-Next.js environments
        }
        throw new Error("Unauthorized: User is not logged in.");
      }
      return res.json();
    })
    .catch((error) => {
      console.error("Error during logout:", error);
      throw error; // Re-throw the error for the caller to handle
    });
}