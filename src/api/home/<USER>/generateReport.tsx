
import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function generateReport(run_id: Number, default_weight: boolean, weight_sim: Number, weight_con: Number, threshold_value: Number, more_info: String) {
    const user = getUser();
    const formData = new FormData();

    const url = `${API_URL}resume/generate_report?run_id=${run_id}&default_weight=${default_weight}&weight_sim=${weight_sim}&weight_con=${weight_con}&threshold_value=${threshold_value}&more_info=${more_info}`;


    return fetch(url, {
        mode: 'cors',
        method: "POST",
        headers: {
            Authorization: `Bearer ${user?.token}`,
        },
    })
        .then((res) => {
            if (res.status === 401) {
                if (typeof window !== 'undefined') {
                    localStorage.removeItem("remainingTime");
                    window.location.href = '/login';  // Redirect if unauthorized
                    throw new Error("Unauthorized");
                }
            }
            if (!res.ok) {
                return res.json().then((error) => {
                    throw new Error(`Upload failed: ${error.msg || error.message}`);
                });
            }
            return res.json();  // Return the response as JSON if successful
        })
        .catch(error => {
            console.error("Error uploading CV:", error);
            throw error;  // Propagate the error to be handled elsewhere
        });
}