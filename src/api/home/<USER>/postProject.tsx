import { API_URL } from '@/utils/constants'
import { getUser } from "@/api/user.localStorage";



// Include this function in your existing API file
export function postProject(client_id: number, project_name: string, description: string) {

  const user = getUser();


  const url = `${API_URL}project/?client_id=${client_id}&project_name=${project_name}&description=${description}`;


  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },

  }).then((res) => {
    if (res.status === 401) {
      if (typeof window !== 'undefined') {
        localStorage.removeItem("remainingTime");
        window.location.href = '/login';
        throw new Error("Unauthorizedddddddddd");
      }
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}