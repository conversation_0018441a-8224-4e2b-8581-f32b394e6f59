import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";
import { Client } from '@/types/LMSTypes';

export function postClient(client_name: string, client_description: string, photo: Client) {
    const user = getUser();

    if (!user?.token) {
        throw new Error("No token found, user not authenticated");
    }

    if (!client_name.trim() || !client_description.trim()) {
        throw new Error("Client name and description are required");
    }

    const formData = new FormData();

    // Always include the `file` field, even if empty
    if (photo.file && photo.file instanceof File) {
        formData.append('file', photo.file);
    } else {
        console.warn("No valid file provided. Sending empty 'file' field.");
        formData.append('file', ""); // Ensure the `file` field is always present
    }

    const url = `${API_URL}client/client_add?name=${encodeURIComponent(client_name)}&description=${encodeURIComponent(client_description)}`;


    return fetch(url, {
        mode: 'cors',
        method: "POST",
        headers: {
            Authorization: `Bearer ${user.token}`,
        },
        body: formData, // Send formData (with or without a file)
    })
        .then(res => {
            if (!res.ok) {
                if (res.status === 401) {
                    if (typeof window !== 'undefined') {
                        localStorage.removeItem("remainingTime");
                        window.location.href = '/login';
                        throw new Error("Unauthorized - Redirecting to login.");
                    }
                }
                throw new Error(`Request failed with status: ${res.status}`);
            }
            return res.json();
        })
        .then(data => {
            return data;
        })
        .catch(error => {
            console.error("Error posting client data:", error.message || error);
            throw error;
        });
}
