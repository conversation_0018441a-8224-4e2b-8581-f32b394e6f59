import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

// Include this function in your existing API file
export function removeUploadedJD(run_id:number) {
  const user = getUser();

  const url = `${API_URL}resume/jd?run_id=${run_id}`;

  return fetch(url, {
    mode: "cors",
    method: "DELETE",
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`,
    },
  }).then((response) => {
    if (response.status === 401) {
      if (typeof window !== 'undefined') {
      window.location.href = "/login";
      throw new Error("Unauthorizedddddddddd");}
    }
    return response.json();
  }) as Promise<any>;
}
