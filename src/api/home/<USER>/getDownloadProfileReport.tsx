import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

// Include this function in your existing API file
export function getDownloadProfileReport(run_id: number, file_type: number) {
  const user = getUser();

  const url = `${API_URL}resume/download_report?run_id=${run_id}&file_type=${file_type}`;


  return fetch(url, {
    mode: "cors",
    method: "GET",
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`,
    },
  })
    .then((response) => {
      if (response.status === 401) {
        if (typeof window !== 'undefined') {
        localStorage.removeItem("remainingTime");
        window.location.href = "/login";
        throw new Error("Unauthorized");}
      }
      return response.blob();  // Return the response as a Blob
    }) as Promise<Blob>;
}
