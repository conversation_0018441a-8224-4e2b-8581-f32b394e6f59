import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function postUploadCV(run_id: number, file: File) {
    const user = getUser();
    
    // Check if user token is available
    if (!user?.token) {
        throw new Error('User not authenticated');
    }

    // Build the URL with run_id as a query parameter
    const url = `${API_URL}resume/cv?run_id=${run_id}`;  // Updated URL

    const formData = new FormData();
    formData.append("cv_file", file);  // Append the file with the key "cv_file"

    return fetch(url, {
        method: "POST",
        headers: {
            Authorization: `Bearer ${user.token}`,  // Pass token for authorization
            // No need to manually set Content-Type, fetch does it for FormData
        },
        body: formData,  // Send FormData as the body
    })
    .then((res) => {
        if (res.status === 401) {   
            if (typeof window !== 'undefined') {
            localStorage.removeItem("remainingTime");
            window.location.href = '/login';  // Redirect if unauthorized
            throw new Error("Unauthorized");}
        }
        if (!res.ok) {
            return res.json().then((error) => {
                throw new Error(`Upload failed: ${error.msg || error.message}`);
            });
        }
        return res.json();  // Return the response as JSON if successful
    })
    .catch(error => {
        console.error("Error uploading CV:", error);
        throw error;  // Propagate the error to be handled elsewhere
    });
}
