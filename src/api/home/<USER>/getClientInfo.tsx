import { API_URL } from '@/utils/constants'
import { getUser } from "@/api/user.localStorage";

export function getClientInfo(client_id: number) {

    const user = getUser();

    const url = `${API_URL}client/${client_id}`;


    return (fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    }).then(response => {
        if (response.status === 401) {
            if (typeof window !== 'undefined') {
                localStorage.removeItem("remainingTime");
                window.location.href = '/login';
                throw new Error("Unauthorizedddddddddd");
            }
        }
        return response.json();
    }
    )) as Promise<any>;
}