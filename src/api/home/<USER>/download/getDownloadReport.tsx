import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

// Include this function in your existing API file
export function getDownloadReport(run_id: number, file_type: number) {
  const user = getUser();

  const url = `${API_URL}resume/download_report?run_id=${run_id}&file_type=${file_type}`;

  return fetch(url, {
    mode: "cors", // CORS configuration, this could be configured at the server side
    method: "GET",
    headers: {
      "Content-Type": "application/json", // Content-Type for the request (may not be needed for GET requests)
      Authorization: `Bearer ${user?.token}`,
    },
  })
    .then((response) => {
      if (response.status === 401) {
        if (typeof window !== 'undefined') {
          localStorage.removeItem("remainingTime");
          window.location.href = "/login";
          throw new Error("Unauthorized");
        }
      }

      if (!response.ok) {
        throw new Error(`Failed to download file. Status: ${response.status}`);
      }

      return response.blob(); // Return the response as a Blob
    })
    .then((blob) => {
      // Generate a custom file name based on file_type
      const timestamp = new Date().toISOString().replace(/[:.-]/g, "_"); // Create a timestamp for uniqueness
      const username = user?.name || "user"; // Get the username, fallback to 'user' if not available
      let customFileName = "";

      switch (file_type) {
        case 1:
          customFileName = `${run_id}_backend_report_${timestamp}.xlsx`;
          break;
        case 2:
          customFileName = `${run_id}_profile_report_${timestamp}.xlsx`;
          break;
        case 3:
          customFileName = `${run_id}_shortlisted_candidate_report_${timestamp}.xlsx`;
          break;
        case 4:
          customFileName = `${run_id}_final_report_${timestamp}.pdf`;
          break;
        default:
          customFileName = `${run_id}_report_${timestamp}.pdf`; // Fallback to a default name
          break;
      }

      // Create a download link for the file
      const downloadLink = document.createElement("a");
      const url = window.URL.createObjectURL(blob);
      downloadLink.href = url;
      downloadLink.download = customFileName; // Set the custom file name
      document.body.appendChild(downloadLink);
      downloadLink.click(); // Trigger the download
      document.body.removeChild(downloadLink); // Clean up the DOM
      window.URL.revokeObjectURL(url); // Revoke the object URL to free up memory
    })
    .catch((error) => {
      console.error("Error downloading the report:", error);
    });
}
