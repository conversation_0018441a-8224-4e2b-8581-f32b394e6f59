
import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function postRunName(project_id: number, run_name: string) {
    const user = getUser();

    const url = `${API_URL}resume/run/?project_id=${project_id}&run_name=${run_name}`;

    return fetch(url, {
        mode: "cors",
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${user?.token}`, // Assuming the user object contains a token
        },

    })
        .then((res) => {
            if (res.status === 401) {
                if (typeof window !== 'undefined') {
                    localStorage.removeItem("remainingTime");
                    window.location.href = '/login';
                    throw new Error("Unauthorized");
                }
            }
            return res.json();
        })
        .catch(error => {
            console.error("Error posting RunName :", error);
            throw error;
        });
}