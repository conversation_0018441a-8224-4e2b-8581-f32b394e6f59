import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function getLogo(fileName: string) {
    const user = getUser();


    // Construct the URL with the file_name query parameter
    const url = `${API_URL}client/display_client_logo/?file_name=${fileName}`;

    return fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    }).then(response => {
        if (response.status === 401) {
            if (typeof window !== 'undefined') {
                localStorage.removeItem("remainingTime");
                window.location.href = '/login';
                throw new Error("Unauthorized access");
            }
        }
        // Instead of json(), use blob() for image data
        return response.blob();
    }).then(blob => {
        // Create a URL for the image blob and return it
        const imageUrl = URL.createObjectURL(blob);
        return imageUrl;
    }).catch(error => {
        console.error("Error fetching logo:", error);
        throw error;
    });
}
