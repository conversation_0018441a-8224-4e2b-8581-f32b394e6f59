import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function editClient(client_id: string, client_name = "", client_description = "", file?: File) {
    const user = getUser();
    const url = `${API_URL}client/edit_client/${client_id}?name=${client_name}&description=${client_description}`;


    // Prepare FormData only if file is provided
    let body: FormData | undefined;
    if (file) {
        body = new FormData();
        body.append('file', file);
    }


    return fetch(url, {
        mode: 'cors',
        method: "PUT",
        headers: {
            Authorization: `Bearer ${user?.token}`
        },
        body: file ? body : undefined, // Only send body if there's a file
    })
    .then((res) => {
        if (res.status === 401) {
            removeTimer();
            window.location.href = '/login';
            throw new Error("Unauthorized");
        }
        if (!res.ok) {
            throw new Error(`Error: ${res.statusText}`);
        }
        return res.json();
    })
    .catch(error => {
        console.error("Error editing client data:", error);
        throw error;
    });
}
