import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function editUserLogo(user_id: number, file: File) {
    const user = getUser();
    
    // Construct the URL with query parameter
    const url = `${API_URL}user/edit_user_logo?user_id=${user_id}`;

    // Prepare FormData to send the file
    const formData = new FormData();
    formData.append('file', file);  // Ensure the 'file' field matches server's expectation

    return fetch(url, {
        mode: 'cors',
        method: "PUT", 
        headers: {
            Authorization: `Bearer ${user?.token}`
        },
        body: formData  // Send file in the body of the request
    })
    .then(async (res) => {
        if (res.status === 401) {
            if (typeof window !== 'undefined') {
            localStorage.removeItem("remainingTime");
            window.location.href = '/login';
            throw new Error("Unauthorized");}
        }
        if (!res.ok) {
            // Read the response body to get more error details
            const errorBody = await res.json();
            console.error("API Error Response:", errorBody);
            throw new Error(`Error: ${res.statusText}, Details: ${JSON.stringify(errorBody)}`);
        }
        return res.json();
    })
    .catch(error => {
        console.error("Error editing user logo:", error);
        throw error;
    });
}

