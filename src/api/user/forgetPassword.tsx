import { API_URL } from "@/utils/constants";
import { UserDetails } from "@/types/LMSTypes";

export function forgetPassword(email: string) {
  // Construct the URL with the email as a query parameter
  const url = new URL(API_URL + "user/forget_password");
  url.searchParams.append('email', email);

  return fetch(url.toString(), {
    mode: 'cors',
    method: "POST",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
    },
  })
  .then((res) => {
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  })
  .catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    throw error; // Rethrow for further handling
  });
}
