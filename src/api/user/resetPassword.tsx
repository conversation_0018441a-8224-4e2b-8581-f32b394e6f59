import { API_URL } from "@/utils/constants";
import { UserDetails } from "@/types/LMSTypes";

export function resetPassword(password: string, token: string) {
  // Construct the URL with password and token as query parameters
  const url = new URL(API_URL + "user/reset_password");
  url.searchParams.append('token', token);
  url.searchParams.append('new_password', password);

  return fetch(url.toString(), {
    mode: 'cors',
    method: "POST",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
    },
  })
  .then((res) => {
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  })
  .catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    throw error; // Rethrow for further handling
  });
}
