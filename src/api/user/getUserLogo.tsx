import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function getUserLogo(file_name: string) {
    const user = getUser();

    const url = `${API_URL}user/user_logo/?file_name=${file_name}`;

    return fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    }).then(response => {
        if (response.status === 401) {
            if (typeof window !== 'undefined') {
                localStorage.removeItem("remainingTime");
                window.location.href = '/login';
                throw new Error("Unauthorized access");
            }
        }

        // Handle the image data as a blob
        return response.blob();
    }).then(blob => {
        // Create a URL for the image blob and return it
        const imageUrl = URL.createObjectURL(blob);
        return imageUrl;
    }).catch(error => {
        console.error("Error fetching user logo:", error);
        throw error;
    });
}
