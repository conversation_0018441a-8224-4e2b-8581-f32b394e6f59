import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export async function refreshToken() {
  const user = getUser();

  // Check if user or token is not available
  if (!user || !user.token) {
    throw new Error("No valid user or token found.");
  }

  try {
    const response = await fetch(`${API_URL}user/refresh_token`, {
      mode: 'cors',
      method: "GET",
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.token}`,
      },
    });

    // Check if the response is OK
    if (!response.ok) {
      // You can check for specific status codes like 401 (Unauthorized)
      throw new Error(`Failed to refresh token: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    // Handle error gracefully
    console.error("Error refreshing token:", error);
    // You can rethrow the error if you want to propagate it or handle it here
    throw error;
  }
}
