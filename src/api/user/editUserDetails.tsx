import { API_URL } from '@/utils/constants';
import { getUser } from "@/api/user.localStorage";

export function editUserDetails(user_id: number, email: string, password: string, phone: number, user_full_name: string) {
    const user = getUser();

    // Construct the URL with query parameters
    const url = `${API_URL}user/edit_user_details?user_id=${user_id}&email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}&phone=${encodeURIComponent(phone)}&user_full_name=${encodeURIComponent(user_full_name)}`;

    return fetch(url, {
        mode: 'cors',
        method: "PUT", // Using GET because the parameters are passed in the URL
        headers: {
            Authorization: `Bearer ${user?.token}`
        }
    })
        .then((res) => {
            if (res.status === 401) {
                if (typeof window !== 'undefined') {
                    localStorage.removeItem("remainingTime");
                    window.location.href = '/login';
                    throw new Error("Unauthorized");
                }
            }
            if (!res.ok) {
                throw new Error(`Error: ${res.statusText}`);
            }
            return res.json();
        })
        .catch(error => {
            console.error("Error editing user details:", error);
            throw error;
        });
}
