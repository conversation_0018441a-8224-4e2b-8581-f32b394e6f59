import { API_URL } from "@/utils/constants.js";
import { UserDetails } from "@/types/LMSTypes"
export function signIn(email:string, password:string) {
  return (fetch(API_URL + "user/login", {
    mode: 'cors',
    method: "POST",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({email: email, password: password})
  })).then((res) => {

    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}