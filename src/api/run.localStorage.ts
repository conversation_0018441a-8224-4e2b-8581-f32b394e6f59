import Cookies from "js-cookie";

const STORAGE_KEY = "appState";

type AppState = {
  selectedRunId: string | null;
  modalType: string | null;
  modalMessage: string;
  fileNameJD: string;
  fileNameCV: string;
  fileNameZip: string;
  file: File | null;
  useDefaultWeights: boolean;
  rangeValues: {
    range1: number;
    range2: number;
  };
  thresholdNumber: number;
  info: string;
  isLoading: boolean;
  isReportGenerated: boolean;
};

const defaultState: AppState = {
  selectedRunId: null,
  modalType: null,
  modalMessage: "",
  fileNameJD: "",
  fileNameCV: "",
  fileNameZip: "",
  file: null,
  useDefaultWeights: true,
  rangeValues: { range1: 100, range2: 100 },
  thresholdNumber: 1,
  info: "",
  isLoading: false,
  isReportGenerated: false,
};

// Save app state to localStorage (only in the browser)
export function saveAppState(state: AppState): void {
  if (typeof window !== "undefined") {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  }
}

// Get app state from localStorage (only in the browser)
export function getAppState(): AppState {
  if (typeof window !== "undefined") {
    const state = localStorage.getItem(STORAGE_KEY);
    return state ? JSON.parse(state) : defaultState;
  }
  return defaultState; // Fallback for SSR
}

// Remove app state from localStorage (only in the browser)
export function removeAppState(): void {
  if (typeof window !== "undefined") {
    localStorage.removeItem(STORAGE_KEY);
  }
}

// Update app state with partial updates
export function updateAppState(updates: Partial<AppState>): void {
  const currentState = getAppState();
  const newState = { ...currentState, ...updates };
  saveAppState(newState);
}

// Set admin cookie (safe for both server and client)
export function setAdminCookie(useDefaultWeights: boolean): void {
  Cookies.set("useDefaultWeights", useDefaultWeights.toString());
}