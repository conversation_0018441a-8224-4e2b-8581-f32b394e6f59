import { UserDetails } from "@/types/LMSTypes";
import Cookies from 'js-cookie';

const USER_LOCAL_STORAGE_KEY = "userDetails";
const TUTORIAL_VISIT_KEY = "hasVisitedTutorial";


export function saveUser(user: UserDetails): void {
  hasAdminRole(user.roles);
  if (typeof window !== 'undefined') {
    localStorage.setItem(USER_LOCAL_STORAGE_KEY, JSON.stringify(user));
  }
}

export function getUser(): UserDetails | undefined {
  if (typeof window !== 'undefined') {
    const user = localStorage.getItem(USER_LOCAL_STORAGE_KEY);
    return user ? JSON.parse(user) : undefined;
  }
}

export function removeUser(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(USER_LOCAL_STORAGE_KEY);
  }
}

export function editUser(updatedUser: Partial<UserDetails>): void {
  const currentUser = getUser();
  if (currentUser) {
    const newUser = { ...currentUser, ...updatedUser };
    saveUser(newUser);
  }
}

function hasAdminRole(roles: Array<{ ROLE: string; SCOPE: string }>): void {
  if (roles.some((role) => role.ROLE.includes("ADMIN"))) {
    Cookies.set('admin', 'true');
  } else {
    Cookies.set('admin', 'false');
  }
}

export function setTutorialVisited(value: boolean): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(TUTORIAL_VISIT_KEY, JSON.stringify(value));
  }
}

export function getTutorialVisited(): boolean | undefined {
  if (typeof window !== 'undefined') {
    const visited = localStorage.getItem(TUTORIAL_VISIT_KEY);
    return visited ? JSON.parse(visited) : false;
  }
}

export function resetTutorialVisit(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TUTORIAL_VISIT_KEY);
  }
}
