import { API_URL } from '@/utils/constants';
import Cookies from 'js-cookie';
import { getUser } from './user.localStorage';

// Define key for client ID in localStorage
const CLIENT_ID_LOCAL_STORAGE_KEY = "clientId";
const user = getUser();

// Function to save client ID to localStorage and Cookies
export async function saveClientId(): Promise<void> {
  try {
    // Define the request body, modify as needed based on API requirements
    const requestBody = {
      username: '<EMAIL>',  // Replace with actual user data
      password: 'abcd1234',       // Replace with actual user data
    };

    const response = await fetch(`${API_URL}user/client_login`, {
      method: "POST",
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user?.token}` // Add the token if required
      },
      body: JSON.stringify(requestBody), // Pass the body if required
    });

    // If the response is not ok, log and throw an error
    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error details:", errorData);  // Log specific error details
      throw new Error('Failed to fetch client ID');
    }

    const data = await response.json();
    const clientId = data.clientId;  // Assuming the response returns { clientId: 'some-id' }

    // Save client ID to localStorage (only in the browser)
    if (typeof window !== "undefined") {
      localStorage.setItem(CLIENT_ID_LOCAL_STORAGE_KEY, clientId);
    }

    // Save client ID in Cookies for global access
    Cookies.set('clientId', clientId);

  } catch (error) {
    console.error('Error fetching client ID:', error);
  }
}

// Function to retrieve client ID from localStorage
export function getClientId(): string | undefined {
  // Only access localStorage in the browser
  if (typeof window !== "undefined") {
    const clientId = localStorage.getItem(CLIENT_ID_LOCAL_STORAGE_KEY);
    return clientId || undefined;
  }
  return undefined;
}

// Function to remove client ID from localStorage and Cookies
export function removeClientId(): void {
  // Only access localStorage in the browser
  if (typeof window !== "undefined") {
    localStorage.removeItem(CLIENT_ID_LOCAL_STORAGE_KEY);
  }
  Cookies.remove('clientId');
}