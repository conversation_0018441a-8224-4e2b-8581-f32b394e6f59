const TIMER_START_TIME_KEY = "START_TIME";

export function saveStartTime(): void {
  const now = Date.now();
  if (typeof window !== 'undefined') {
    localStorage.setItem(TIMER_START_TIME_KEY, now.toString());
  }
}

export function getStartTime(): number | null | undefined {
  if (typeof window !== 'undefined') {
    const startTime = localStorage.getItem(TIMER_START_TIME_KEY);
    return startTime ? parseInt(startTime, 10) : null;
  }
}

export function removeTimer(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TIMER_START_TIME_KEY);
  }
}

export function calculateRemainingTime(
  timeDetail: number
): { remainingTime: number; elapsed: number } {
  const startTime = getStartTime();
  if (!startTime) {
    return { remainingTime: timeDetail, elapsed: 0 };
  }
  const elapsed = Math.floor((Date.now() - startTime) / 1000);
  const remainingTime = Math.max(timeDetail - elapsed, 0);
  return { remainingTime, elapsed };
}

export function shouldShowModal(remainingTime: number): boolean {
  return remainingTime <= 300; // Show modal when 5 minutes or less remain
}
