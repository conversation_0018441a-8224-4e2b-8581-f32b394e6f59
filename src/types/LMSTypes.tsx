
//Roles and scope
interface roles {
    ROLE: string;
    SCOPE: string;
  }
  
//User
export interface UserDetails {
    user_id: number;
    email: string;
    phone: string;
    user_full_name: string;
    roles: Array<roles>;
    company: string | null;
    company_id: number | null;
    groups: Object;
    token: string;
    client_id:number;
  }

  export interface ScheduleVM {
    start_time: string;
    stop_time: string;
    schedule_days: string;
    
  }
  export interface UserEmail {
    email_file: File; 
  }

  export interface Budget {
    resourceName: string;
    virtualMachine: string;
    subscriptionid: string;
    publicip: string;
    budgetname: string;
    budget: number;
    contactemails:string;
  }

  export interface Client {
    file: string;
  }
  
  export interface UploadJD {
    file: string;
  }
  export interface UploadCV {
    file: string;
  }
  