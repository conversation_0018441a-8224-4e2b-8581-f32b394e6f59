"use client"
import ParticlesBackground from '@/components/ui/Design'
import Footer from '@/components/ui/Footer';
import { WavyBackground } from "@/components/ui/wavy-background";
import React from 'react'

const page = () => {
    return (
        <main className="w-full h-[200vh] bg-gradient-to-b from-violet-900 via-slate-900 to-slate-900 overflow-x-hidden flex flex-col justify-between">
            <WavyBackground className="overflow-auto flex justify-center items-center">
                <div className="bg-white/5 backdrop-blur-md text-2xl md:text-4xl  text-white  inter-var text-center lg:w-[900px] lg:h-fit flex flex-col gap-6 py-20 px-6 lg:py-2 lg:px-2">
                    <h1 className='w-full h-fit text-left lg:text-7xl font-bold'>About us</h1>
                    <p className='text-[15px] font-semibold text-left'>In today’s fast-paced job market, HR teams are under pressure to find the right candidates quickly and efficiently. HR-<PERSON> is here to change the game by leveraging cutting-edge AI technology to streamline hiring processes and ensure better, data-driven decisions.

                        By using machine learning algorithms, HR-Math analyzes job descriptions, candidate resumes, and even social profiles to identify the best-fit candidates in a fraction of the time. The platform goes beyond keyword matching, assessing skills, experience, and cultural fit to present a shortlist of applicants who are most likely to succeed in the role.

                        What sets HR-Math apart is its ability to continuously learn and adapt. As more data is fed into the system, the AI improves its accuracy and reduces biases, helping HR teams to make fair, objective decisions. Whether it’s screening applications, scheduling interviews, or even predicting a candidate’s future performance, HR-Math is your AI-powered hiring partner.

                        With HR-Math, businesses can save time, reduce human error, and ultimately hire smarter. It’s not just about finding talent—it’s about finding the right talent, faster and more effectively.</p>
                </div>
                
            </WavyBackground>
            <Footer/>
        </main>
    )
}

export default page