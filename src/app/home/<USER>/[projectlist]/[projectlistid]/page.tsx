"use client";
import React, { useEffect, useState } from "react";

import {
  ArrowPathRoundedSquareIcon,
  InboxArrowDownIcon,
  PaperAirplaneIcon,
  PlusCircleIcon,
} from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useGetRunList } from "@/hooks/home/<USER>/useGetRunList";
import { useGetDownloadProfileReport } from "@/hooks/home/<USER>/useGetDownloadProfileReport";
import SimplifiedRunListCard from "@/components/home/<USER>/projectList/SimplifiedRunListCard";

export default function Page() {
  const [selectedRunId, setSelectedRunId] = useState<number | null>(null); // Track selected run_id for downloading the profile report
  const [isDownloading, setIsDownloading] = useState(false);
  const [fileType, setFileType] = useState<number>(3);
  const params = useParams();

  const ProjectIdvalue = parseInt(params.projectlistid, 10);

  // Fetch the list of runs
  const {
    data: runlistdata,
    isLoading,
    isError,
  } = useGetRunList(ProjectIdvalue);

  const {
    data: downloadData,
    isLoading: downloadLoading,
    isError: downloadError,
    refetch: refetchDownloadReport,
  } = useGetDownloadProfileReport(selectedRunId, fileType);


  useEffect(() => {
    if (selectedRunId && !downloadLoading && !downloadError && downloadData) {

      const blob = downloadData;

      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `report_${selectedRunId}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        setIsDownloading(false);
      }
    }
  }, [selectedRunId, downloadLoading, downloadError, downloadData]);

  const handleDownloadClick = (
    run_id: number,
    reportType: "profile" | "shortlist" | "final"
  ) => {
    setSelectedRunId(run_id); // Set selected run_id for download
    setIsDownloading(true); // Set the downloading status

    const fileType = reportType === "profile" ? 2 :
      reportType === "shortlist" ? 3 :
        reportType === "final" ? 4 : "";

    setFileType(fileType); // Update the fileType state
    refetchDownloadReport(); // Refetch the report for the selected run_id with the correct fileType
  };

  // if (downloadLoading) {
  //   return (
  //     <main className="w-full h-full flex justify-center items-center">
  //       <span className="text-2xl">Loading...</span>
  //     </main>
  //   )
  // }

  if (isLoading) {
    return (
      <main className="w-full h-full flex flex-col justify-center items-center">
        <div className="flex justify-end p-4">
          <Link href={`/home/<USER>
            <button className="inline-flex h-12 animate-shimmer items-center justify-between rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50 w-[180px]">
              Create Run<PlusCircleIcon className="text-white w-6 h-6" />
            </button>
          </Link>
        </div>
        <div className="w-full h-full flex justify-center items-center">
          <span className="text-4xl">Loading...</span>
        </div>
      </main>
    );
  }

  if (!runlistdata || runlistdata.err) {
    return (
      <div className="flex flex-col h-full w-full">
        <div className="flex justify-end p-4">
          <Link href={`/home/<USER>
            <button className="inline-flex h-12 animate-shimmer items-center justify-between rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50 w-[180px]">
              Create Run<PlusCircleIcon className="text-white w-6 h-6" />
            </button>
          </Link>
        </div>
        <div className="w-full h-full flex justify-center items-center">
          <span className="text-4xl">No Run Found!</span>
        </div>
      </div>
    );
  }

  if (downloadError) {
    return <div>Error fetching modules</div>;
  }


  else if (downloadError) {
    return <div>Error fetching modules</div>;
  }

  else if (runlistdata) {
    const sortedrunlistData = runlistdata?.sort((a, b) => new Date(a.creation_date) - new Date(b.creation_date));
    return (
      <div className="w-full h-full flex-col px-1">
        <div className="flex flex-col h-[85vh]">
          <div className="flex flex-col gap-2 justify-start items-start w-full rounded-md h-full">
            <div className="flex flex-col lg:flex-row justify-between lg:gap-0 gap-2 w-full px-3 py-4 h-fit">

              <Heading pgHeading="List Of Runs" textColor="text-slate-500 text-md" />
              {/* Display the Project Name once at the top */}
              <div>
                {sortedrunlistData[0]?.project_name && (
                  <div className="text-md lg:text-xl font-semibold text-slate-700">
                    Project Name : {sortedrunlistData[0].project_name}
                  </div>
                )}
              </div>

              <Link href={`/home/<USER>
                <button className="inline-flex h-12 animate-shimmer items-center justify-between rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50 w-[180px]">
                  Create Run<PlusCircleIcon className="text-white w-6 h-6" />
                </button>
              </Link>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4 h-fit w-full overflow-auto">
              {runlistdata?.map((run) => (
                <SimplifiedRunListCard
                  key={run.run_id}
                  sortedrunlistData={run}
                  handleDownloadClick={handleDownloadClick}
                  ProjectIdvalue={ProjectIdvalue}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
}
