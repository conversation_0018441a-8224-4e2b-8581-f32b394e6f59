"use client";
import Heading from "@/components/ui/Heading";
import SimplifiedProjectCard from "@/components/home/<USER>/SimplifiedProjectCard";
import { useGetProjectsWithClientId } from "@/hooks/home/<USER>/useGetProjectsWithClientId";
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { PlusCircleIcon } from "@heroicons/react/20/solid";
import { FolderIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import React, { useState, useEffect } from 'react';
import { getUser } from "@/api/user.localStorage";
import { useGetAllClientsWithUserId } from '@/hooks/home/<USER>/useGetAllClientsWithUserId';
import { useParams } from "next/navigation"; // If using the App Routerimport { useQuery, useQueryClient } from '@tanstack/react-query'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useSearchParams } from 'next/navigation';
import ClientIDSelector from '@/components/home/<USER>/ClientIDSelector';
import { useGetProjectsWithUserId } from '@/hooks/home/<USER>/useGetProjectsWithUserId'

// Get QueryClient from the context


export default function Page() {
  // Get the user and clientId
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [url, setUrl] = useState(window.location.href)
  const searchParams = useSearchParams();
  const clientId = searchParams.get('clientId');
  const user = getUser();
  const queryClient = useQueryClient()
  // State to track the currently hovered project index (not used in code yet)
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const [modalType, setModalType] = useState<string | null>(null);  // For the type of modal (msg, err)
  const [modalMessage, setModalMessage] = useState("");  // For the message of the modal
  const [projectFinalList, setProjectFinalList] = useState<any>([]);
  const [clientListDataInfo, setClientListDataInfo] = useState()
  const [selectedClientId, setSelectedClientId] = useState<any>(clientId);
  // const { clientId } = params;


  // Fetch project data
  const { data: projectlistdata, isLoading: projectlistdataLoading } = useGetProjectsWithClientId(selectedClientId || "No");

  const { data: allprojectlistdata, isLoading: allprojectlistdataLoading } = useGetProjectsWithUserId(user?.user_id);

  const { data: clientListInfo, isLoading: clientListInfoLoading } = useGetAllClientsWithUserId(user?.user_id);

  useEffect(() => {
    setSelectedClientId(selectedClientId)
  }, [selectedClientId])


  let AllClientInfo = {
    "created_by": "All",
    "name": "Display all Clients",
    "logo": null,
    "description": "All",
    "client_id": null,
    "user_id": null,
    "creation_date": "All"
  }



  useEffect(() => {
    if (!selectedClientId) {
      setProjectFinalList(allprojectlistdata || []);
    } else {
      setProjectFinalList(projectlistdata || []);
    }
  }, [selectedClientId, projectlistdata, allprojectlistdata]);


  useEffect(() => {
    if (clientListInfo == "No value found") {
      setModalType("err")
      setModalMessage("Please create a client first")
      setTimeout(() => {
        window.location.href = `/home`
      }, 2500);
      return;
    }
  }, [clientListInfo])

  // Toggle dropdown visibility
  const toggleDropdown = () => {
    setDropdownVisible(!dropdownVisible);
  };

  useEffect(() => {
    if (Array.isArray(clientListInfo)) {
      setClientListDataInfo([AllClientInfo, ...clientListInfo]);
    } else {
      setClientListDataInfo([AllClientInfo]); // Handle the case where clientListInfo is not an array
    }
  }, []);


  // Sorting function
  const sortByClientId = (arr) => {
    return arr?.sort((a, b) => {
      // Handle null values for client_id
      if (a.client_id === null) return -1;
      if (b.client_id === null) return 1;
      return a.client_id - b.client_id;
    });
  }

  // Replace the direct call to push() with a safer approach

  useEffect(() => {
    if (Array.isArray(clientListInfo)) {
      setClientListDataInfo([AllClientInfo, ...clientListInfo]);
    } else {
      setClientListDataInfo([AllClientInfo]); // If clientListInfo is not an array, set it with just the default client
    }
  }, [clientListInfo]);


  if (projectlistdataLoading && allprojectlistdataLoading && clientListInfoLoading) {
    return (
      <div className="w-full h-full flex justify-center items-center">
        <div className="text-center">
          <span className="text-2xl">Loading...</span>
          {/* You can also add a spinner or animation here */}
        </div>
      </div>
    );
  }


  else if (!Array.isArray(projectFinalList) || projectFinalList.length === 0) {
    const sortedClientData = sortByClientId(clientListDataInfo);

    return (
      <div className="w-full h-full flex-col px-1">
        {modalMessage && modalType && (
          <ResponseModal
            type={modalType}
            message={modalMessage}
            onClose={() => {
              setModalType(null);
              setModalMessage("");
            }}
          />
        )}
        <div className="w-full h-fit p-2 pt-4 px-2 lg:px-4">
          <ClientIDSelector
            clientData={sortedClientData}
            setSelectedClientId={setSelectedClientId}
            currentClientId={selectedClientId}
            disableDropdown={clientListDataInfo?.includes("No value found")}
          />
        </div>

        <div className="flex flex-col h-[90vh] overflow-auto justify-center items-center">
          <div className="text-center py-4">
            <p className="text-lg">No project data available.</p>
          </div>
          <button
            className="inline-flex h-12 animate-shimmer items-center justify-between rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50 w-fit lg:w-[250px]"
            onClick={() => window.location.href = `/home/<USER>/create/${selectedClientId}?clientId=${selectedClientId}`}
          >
            Create Project
            <PlusCircleIcon className="w-7 h-7" />
          </button>

        </div>
      </div>
    );
  }

  else if (Array.isArray(projectFinalList) && projectFinalList.length > 0) {
    const sortedClientData = sortByClientId(clientListDataInfo);
    const sortedprojectlistData = projectFinalList?.sort((a, b) => {
      const dateA = new Date(a.creation_date);
      const dateB = new Date(b.creation_date);
      return dateA - dateB;
    }


    );
    return (
      <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
        {modalMessage && modalType && (
          <ResponseModal
            type={modalType}
            message={modalMessage}
            onClose={() => {
              setModalType(null);
              setModalMessage("");
            }}
          />
        )}

        {/* Header following design guide */}
        <div className="mb-6">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
            Project Management
          </h1>

          {/* Search and controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="w-full max-w-md">
              <ClientIDSelector
                clientData={sortedClientData}
                setSelectedClientId={setSelectedClientId}
                currentClientId={selectedClientId} />
            </div>
            <button
              className="inline-flex items-center justify-center rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 gap-2"
              onClick={() => window.location.href = `/home/<USER>/create/${selectedClientId}?clientId=${selectedClientId}`}
            >
              Create Project
              <PlusCircleIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Card Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {projectFinalList?.map((project) => {
            return (
              <SimplifiedProjectCard
                key={project.project_id} // Use project_id as the key instead of index
                project_id={project.project_id}
                project_name={project.project_name}
                run_is_present={project.run_is_present}
                client_name={project.client_name}
                project_description={project.description}
                creation_date={project.creation_date}
                selectedClientId={selectedClientId}
              />
            );
          })}
        </div>
      </main>
    );
  }
  else {
    return (
      <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
        <div className="mb-6">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
            Project Management
          </h1>

          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex items-center text-gray-700">
              <span>No projects found</span>
            </div>
            <button
              className="inline-flex items-center justify-center rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 gap-2"
              onClick={() => window.location.href = `/home/<USER>/create/null?clientId=null`}
            >
              Create Project
              <PlusCircleIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="text-center py-12">
          <FolderIcon className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
          <p className="text-gray-500 mb-6 max-w-sm mx-auto">Get started by creating your first project.</p>
        </div>
      </main>
    )
  }
}
