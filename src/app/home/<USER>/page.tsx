"use client";

import React, { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useGetViewReport } from "@/hooks/home/<USER>/useGetViewReport";
import Heading from "@/components/ui/Heading";
import Card from "@/components/home/<USER>/Card";

// Define the expected shape of the response
interface ReportResponse {
  JD_Requirement: string;
  Candidate_Name: string;
  Similarity_Score: number;
  Consistency_Score: number;
  Reasoning_for_Similarity_Score: string;
  Reasoning_for_Consistency_Score: string;
  Final_Score: number;
  Final_Rank: number;
  Absolute_Ranking: any;
}

export default function Page() {
  const searchParams = useSearchParams();
  const runId = searchParams.get("runId");
  const fileType = searchParams.get("file_type") || "1"; // Default to 1 if file_type is not provided

  const [isZip, setIsZip] = useState(false);
  const [finalData, setFinalData] = useState<ReportResponse[]>([]);

  useEffect(() => {
    const isZipParam = searchParams.get("is_zip_present");
    setIsZip(isZipParam === "true");
  }, [searchParams]);

  const { data, isLoading, isError, error } = useGetViewReport(runId, fileType);

  useEffect(() => {
    if (data) {
      try {
        const parsedData = JSON.parse(data);
        if (Array.isArray(parsedData)) {
          setFinalData(parsedData);
        } else {
          console.error("Parsed data is not an array:", parsedData);
        }
      } catch (err) {
        console.error("Failed to parse JSON:", err);
      }
    }
  }, [data]);

  const renderHeader = () => (
    <div className="flex md:flex-row flex-col py-4 w-full p-1 justify-between gap-4 ">
      <Heading
        pgHeading={parseInt(fileType, 10) === 2 ? "Profile Ranking Report" : "Shortlist Report"}
        textColor={"text-slate-200"}
      />
      <a
        href={`/home/<USER>
        className={`${parseInt(fileType, 10) === 2 ? "bg-secondary" : "bg-slate-500"
          } text-white px-2 py-[4px] rounded-md text-center md:text-left`}
      >
        View Shortlist Report
      </a>
      <a
        href={`/home/<USER>
        className={`${parseInt(fileType, 10) === 3 ? "bg-secondary" : "bg-slate-500"
          } text-white px-2 py-[4px] rounded-md text-center md:text-left`}
      >
        View Profile Ranking Report
      </a>
    </div>
  );

  const renderCards = () => {
    if (finalData.length > 0) {
      return finalData.map((item, index) => (
        <Card
          key={index}
          JD_Requirement={item.JD_Requirement}
          Candidate_Name={item.Candidate_Name}
          Final_Rank={item.Final_Rank}
          Final_Score={item.Final_Score}
          Consistency_Score={item.Consistency_Score}
          Similarity_Score={item.Similarity_Score}
          Reasoning_for_Consistency_Score={item.Reasoning_for_Consistency_Score}
          Reasoning_for_Similarity_Score={item.Reasoning_for_Similarity_Score}
        />
      ));
    }
    return <div>Nothing to display</div>;
  };

  if (isLoading) return <div>Loading reports...</div>;
  if (isError) return (
    <div>
      Error loading reports: {error instanceof Error ? error.message : "Unknown error"}
    </div>
  );

  return (
    <div className="w-full h-full flex flex-col px-1">
      <div className="flex flex-col h-full overflow-hidden">
        {renderHeader()}
        <div className="grid grid-cols-1 sm:items-center lg:items-start lg:grid-cols-2 gap-10 overflow-y-auto">
          {renderCards()}
        </div>
      </div>
    </div>
  );
}
