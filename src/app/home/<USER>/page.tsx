'use client'
import ImageView from '@/components/home/<USER>/ImageView';
import MainScreen from '@/components/home/<USER>/MainScreen';
import { useGetUserDetailsWithLogo } from '@/hooks/user/useGetUserDetailsWithLogo';
import React, { useState } from 'react'
import { getUser } from "@/api/user.localStorage"
import ResponseModal from "@/components/ui/extra/ResponseModal";

const Page = () => {
  const user = getUser();
  const { data: userDataInfo, isLoading, isError } = useGetUserDetailsWithLogo(user?.user_id);
  const [showImage, setshowImage] = useState(false)
  const [imageData, setImageData] = useState<any>()
  const [activeState, setActiveState] = useState(0)
  const [modalType, setModalType] = useState<string | null>(null);  // For the type of modal (msg, err)
  const [modalMessage, setModalMessage] = useState("");  // For the message of the modal

  const topBarOptions = [
    "User"
  ]

  if (isLoading) {
    return <div>Loading...</div>; // Show a loading indicator while fetching data
  }

  if (isError) {
    return <div>Error loading client info</div>; // Handle error state
  }

  if (!userDataInfo) {
    return <div>No client info available</div>; // Show message if no client info is found
  }

  else {
    return (
      <main className="flex flex-col lg:flex-row w-screen lg:justify-center pb-16 lg:pb-12 lg:items-center h-full lg:h-full overflow-auto relative lg:px-10">
        {showImage && (
          <ImageView
            clientInfo={imageData}
            setshowImage={setshowImage} />
        )}

        {modalType && modalMessage && (
          <ResponseModal
            type={modalType}
            message={modalMessage}
            onClose={() => {
              setModalType(null);
              setModalMessage(""); // Reset modal state on close
            }}
          />
        )}

        <div className="flex flex-col lg:justify-center lg:items-center w-full h-fit lg:h-full pt-4">
          <div className='w-full max-w-xl h-fit bg-violet-950/60 backdrop-blur-md rounded-xl flex justify-center py-1'>
            {topBarOptions.map((item) => (
              <button
                key={item}
                className='text-[14px] text-slate-200 hover:text-slate-800 hover:bg-white duration-300 rounded-xl py-1 px-8'>
                  {item}
                </button>  // Render the item in a div or your preferred component
            ))}
          </div>
          <MainScreen
            userDataInfo={userDataInfo}
            setshowImage={setshowImage}
            setImageData={setImageData}
            activeState={activeState}
            setModalType={setModalType}
            setModalMessage={setModalMessage}
            user={user}
          />
        </div>
      </main>

    );
  }


}

export default Page;
