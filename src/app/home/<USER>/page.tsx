"use client"
import { useGetAllClientsWithUserId } from '@/hooks/home/<USER>/useGetAllClientsWithUserId';
import React, { useState } from 'react'
import { getUser } from "@/api/user.localStorage"
import SimplifiedClientCard from '@/components/home/<USER>/SimplifiedClientCard';
import ImageView from '@/components/home/<USER>/ImageView';
import { PlusCircleIcon } from "@heroicons/react/20/solid";
import { BuildingOffice2Icon } from "@heroicons/react/24/outline";

const page = () => {
    const user = getUser();
    const { data: clientListInfo, isLoading: clientListInfoLoading } = useGetAllClientsWithUserId(user?.user_id);
    const [showImage, setshowImage] = useState(false)
    const [imageData, setImageData] = useState<any>()

    const sortByClientId = (arr: any[]) => {
        return arr.sort((a: any, b: any) => a.client_id - b.client_id);
    }

    if (clientListInfoLoading) {
        return (
            <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
                <div className="animate-pulse">
                    {/* Header skeleton */}
                    <div className="mb-6">
                        <div className="h-8 bg-gray-200 rounded w-48 mb-4"></div>
                        <div className="flex gap-4">
                            <div className="h-10 bg-gray-200 rounded w-64"></div>
                            <div className="h-10 bg-gray-200 rounded w-32"></div>
                        </div>
                    </div>

                    {/* Grid skeleton */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        {Array.from({ length: 6 }).map((_, i) => (
                            <div key={i} className="bg-gray-200 h-64 rounded-lg"></div>
                        ))}
                    </div>
                </div>
            </main>
        )
    }
    else if (clientListInfo && !clientListInfo.includes("No value found")) {
        const sortedClientData = sortByClientId(clientListInfo);
        return (
            <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
                {showImage && <ImageView clientInfo={imageData} setshowImage={setshowImage} />}

                {/* Header following design guide */}
                <div className="mb-6">
                    <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
                        Client Management
                    </h1>

                    {/* Search and controls */}
                    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                        <div className="flex items-center text-gray-700">
                            <span>Total Clients: {clientListInfo?.length}</span>
                        </div>
                        <button
                            className="inline-flex items-center justify-center rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 gap-2"
                            onClick={() => window.location.href = '/home'}
                        >
                            Create Client
                            <PlusCircleIcon className="w-5 h-5" />
                        </button>
                    </div>
                </div>
                <div className="flex-row md:justify-start justify-around items-start w-full h-full lg:my-0 grid sm:grid-cols-2 lg:grid-cols-3 gap-6 px-4 md:px-6 lg:px-8 overflow-y-auto overflow-x-hidden">
                    {sortedClientData.map((item: any, index: number) => {
                        return <SimplifiedClientCard
                            key={index}
                            clientListInfo={item}
                            setImageData={setImageData}
                            setshowImage={setshowImage} />
                    })}
                </div>
            </main>
        )
    } else {
        return (
            <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
                <div className="mb-6">
                    <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
                        Client Management
                    </h1>

                    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                        <div className="flex items-center text-gray-700">
                            <span>Total Clients: 0</span>
                        </div>
                        <button
                            className="inline-flex items-center justify-center rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 gap-2"
                            onClick={() => window.location.href = '/home'}
                        >
                            Create Client
                            <PlusCircleIcon className="w-5 h-5" />
                        </button>
                    </div>
                </div>

                <div className="text-center py-12">
                    <BuildingOffice2Icon className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
                    <p className="text-gray-500 mb-6 max-w-sm mx-auto">Get started by creating your first client.</p>
                </div>
            </main>
        )
    }

}

export default page