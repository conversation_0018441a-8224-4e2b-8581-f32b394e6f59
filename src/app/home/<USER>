"use client";
import React, { useState, useEffect } from "react";
import { usePostClient } from "@/hooks/home/<USER>/usePostClient";
// import ClientForm from "@/components/home/<USER>/ClientFormcopy"; // Import the ClientForm component
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { useGetAllClientsWithUserId } from "@/hooks/home/<USER>/useGetAllClientsWithUserId";
import { getUser } from "@/api/user.localStorage";
import { ClientForm } from "@/components/home/<USER>/ClientForm";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { TypewriterEffectSmooth } from "@/components/ui/typewriter-effect";

export default function Page() {
  const [clientName, setClientName] = useState<string>("");
  const [clientDescription, setClientDescription] = useState<string>("");
  const [file, setFile] = useState<File | null>(null);
  const [errors, setErrors] = useState<{ name?: string; description?: string; file?: string }>({});
  const [isClientSubmitted, setIsClientSubmitted] = useState<boolean>(false);
  const [modalType, setModalType] = useState<string | null>(null);  // For the type of modal (msg, err)
  const [modalMessage, setModalMessage] = useState("");

  const createClient = usePostClient(clientName, clientDescription, { file });
  const user = getUser();

  const validateFields = (): boolean => {
    const newErrors: { name?: string; description?: string } = {};
    if (!clientName) newErrors.name = "Client name is required.";
    if (!clientDescription) newErrors.description = "Client description is required.";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePostClientData = async () => {
    if (!validateFields()) {
      return;
    }

    try {


      const response = await createClient.mutateAsync({
        client_name: clientName,
        client_description: clientDescription,
        file: file,
      });
      // Set modal text with response message
      setIsClientSubmitted(true);
      if (response.msg) {

        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        if (typeof window !== 'undefined') {
        setTimeout(() => {
          window.location.href = `/home/<USER>
        }, 2000);
      }

      } else if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      } else if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      } else {
      }
    } catch (error) {
      console.error("Error creating client:", error);
      setModalType("err");  // Set the modal type to "err"
      setModalMessage(`Error: ${error}`);
    }
  };
  const pdfUrl = "public/tutorials/sample.pdf";

  // Get clients data based on user ID
  const { data: clientsData } = useGetAllClientsWithUserId(user?.user_id);

  const words1 = `Smart Hiring, Revolutionizing Recruitment`;
  const words2 = [{
    text: "HR-Math",
    className: "text-slate-200 text-[60px]",
  }]
  const words3 = `Leverage AI to find the perfect match for every role`;


  return (
    <main className="w-full h-full lg:h-[100vh] bg-white overflow-auto flex flex-col justify-center items-center">
      <div className="flex flex-col md:flex-row justify-between w-full h-full p-2 mb-20 gap-10 overflow-auto">

        <div className="flex flex-col lg:p-28 h-fit">
          <div className="h-[100px] w-[300px]">
            <TextGenerateEffect className="text-lg text-gray-800" words={words1} />
          </div>
          <div className="h-[100px] w-[300px]">
            <TypewriterEffectSmooth words={[{
              text: "HR-Math",
              className: "text-blue-600 text-[60px]",
            }]} />
          </div>

          <TextGenerateEffect className="text-lg text-gray-700" words={words3} />
        </div>


        <div className="flex justify-center items-center rounded-lg w-full h-full relative">
          <ClientForm
            clientName={clientName}
            clientDescription={clientDescription}
            setClientName={setClientName}
            setClientDescription={setClientDescription}
            setFile={setFile}
            errors={errors}
            handlePostClientData={handlePostClientData}
          />
        </div>
      </div>
      {/* Modal for submission confirmation */}
      {modalType && modalMessage && (
        <ResponseModal
          type={modalType}
          message={modalMessage}
          onClose={() => {
            setModalType(null);
            setModalMessage("");
            setIsClientSubmitted(false);
          }}
        />
      )}

    </main>
  );
}
