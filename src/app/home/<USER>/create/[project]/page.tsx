"use client"
import React, { useState, useEffect } from 'react';
import { getUser } from '@/api/user.localStorage';
import { usePostProject } from '@/hooks/home/<USER>/usePostProject';
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { useGetAllClientsWithUserId } from '@/hooks/home/<USER>/useGetAllClientsWithUserId';
import ClientIDSelector from '@/components/home/<USER>/ClientIDSelector';
import { useSearchParams } from 'next/navigation';
import ProjectForm from '@/components/home/<USER>/ProjectForm';

export default function Page() {
  const [projectName, setProjectName] = useState('');
  const [description, setDescription] = useState('');
  const [errors, setErrors] = useState({ projectName: '', description: '' });
  const [isProjectSubmitted, setIsProjectSubmitted] = useState(false);
  const [noclientidModal, setNoclientidModal] = useState(false);
  const [modalType, setModalType] = useState<string | null>(null);
  const [modalMessage, setModalMessage] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const searchParams = useSearchParams();
  const clientId = searchParams.get('clientId');
  const user = getUser();
  const [selectedClientId, setSelectedClientId] = useState<any>(clientId);

  const createProject = usePostProject(parseInt(selectedClientId), projectName, description);

  const { data: clientListInfo } = useGetAllClientsWithUserId(user?.user_id);

  useEffect(() => {
    if (clientListInfo == "No value found") {
      setModalType("err")
      setModalMessage("Cannot Create a Project without a Client")
      setTimeout(() => {
        window.location.href = `/home`
      }, 2500);
      return;
    }
  }, [clientListInfo]);

  const validateForm = () => {
    const newErrors = { projectName: '', description: '' };

    if (!projectName.trim()) {
      newErrors.projectName = 'Project name is required';
    }
    if (!description.trim()) {
      newErrors.description = 'Project Description is required';
    }

    setErrors(newErrors);
    return !newErrors.projectName && !newErrors.description;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (selectedClientId == null || selectedClientId == "null") {
      setModalType("err");
      setModalMessage("Please Select client first!");
      return;
    }

    try {
      const response = await createProject.mutateAsync({
        client_id: selectedClientId,
        project_name: projectName,
        description: description,
      });

      if (response.msg) {
        setModalType("msg");
        setModalMessage(response?.msg);
        setIsProjectSubmitted(true);
        setTimeout(() => {
          window.location.href = `/home/<USER>/${selectedClientId}?clientId=${selectedClientId}`;
        }, 1500);
      } else if (response.err) {
        setModalType("err");
        setModalMessage(response?.err);
        setIsProjectSubmitted(true);
      } else if (response.detail) {
        setModalType("err");
        setModalMessage(response?.detail);
        setIsProjectSubmitted(true);
      }
    } catch (error) {
      console.error("Error creating project:", error);
      alert("Error submitting data. Please check your input and try again.");
    }
  };

  if (clientListInfo) {
    const sortedClientData = clientListInfo.sort((a, b) => a.client_id - b.client_id);
    return (
      <div className="w-full h-full flex-col px-1">
        <main className="flex flex-col h-full lg:h-[90vh] overflow-y-auto overflow-x-hidden gap-10 lg:gap-0">
          {/* Client ID selector */}
          <div className="w-full h-fit p-2">
            <ClientIDSelector
              clientData={sortedClientData}
              setSelectedClientId={setSelectedClientId}
              currentClientId={selectedClientId}
              disableDropdown={sortedClientData?.includes("No value found")}
            />
          </div>

          {/* Project form */}
          <ProjectForm
            projectName={projectName}
            setProjectName={setProjectName}
            description={description}
            setDescription={setDescription}
            errors={errors}
            handleSubmit={handleSubmit}
          />

          {/* Modal for submission confirmation */}
          {modalMessage && modalType && (
            <ResponseModal
              type={modalType}
              message={modalMessage}
              onClose={() => {
                setModalType(null);
                setModalMessage("");
              }}
            />
          )}
        </main>
      </div>
    );
  } else {
    return <div></div>;
  }
}
