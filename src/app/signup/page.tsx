"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic"; // Import next/dynamic
// import { signUp } from "@/api/user/userSignUp";  // Create a new signUp API call
import { API_URL } from '@/utils/constants';
import SignUpComponent from "@/components/signup/SignUpComponent";
import ParticlesBackground from "@/components/ui/Design";
import { AnimatePresence, motion } from "framer-motion";

const ForgetPassword = dynamic(() => import("@/components/forgotpassword/ForgetPassword"), {
  ssr: false,
  loading: () => <p>Loading...</p>,
});

export default function SignUpPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  //   const [firstName, setFirstName] = useState("");
  //   const [lastName, setLastName] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();

  const handleSignUp = async () => {
    if (password !== confirmPassword) {
      setErrorMessage("Passwords do not match.");
      return;
    }
    // const newUser = await signUp(firstName, lastName, email, password);
    // if (newUser?.detail) {
    //   setErrorMessage(newUser.detail);
    // } else {
    //   router.push("/login"); // Redirect to login after successful signup
    // }
  };

  return (
    <main className="w-full h-[100vh] bg-transparent flex flex-col justify-center items-center pb-10 pt-20 lg:p-0">

      <motion.div
        className="z-40 mt-40 lg:mt-10 flex justify-center items-center"
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          duration: 0.8,
          delay: 0.5,
          ease: [0, 0.71, 0.2, 1.01],
        }}>
        <SignUpComponent
          email={email}
          setEmail={setEmail}
          password={password}
          setPassword={setPassword}
          confirmPassword={confirmPassword}
          setConfirmPassword={setConfirmPassword}
          //   firstName={firstName}
          //   setFirstName={setFirstName} 
          //   lastName={lastName}
          //   setLastName={setLastName}
          handleSignUp={handleSignUp}
          errorMessage={errorMessage}
        />
      </motion.div>
      {false && (
        <ForgetPassword isOpen={true} onClose={() => { }} />
      )}
    </main>
  );
}
