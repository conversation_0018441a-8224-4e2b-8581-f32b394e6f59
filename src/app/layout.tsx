import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import Providers from "@/utils/provider"

import axios from 'axios';
import PreHeader from '@/components/ui/PreHeader';
import Footer from '@/components/ui/Footer';

// Setup Axios interceptor
axios.interceptors.response.use(response => response, error => {
  if (error.response && error.response.status === 401) {
    if (typeof window !== "undefined") {
      window.location.href = '/login';
    }
  }
  return Promise.reject(error);
});

const inter = Inter({ subsets: ['latin'] })


export const metadata: Metadata = {
  title: 'HR-MATH',
  description: 'Modern Recruitment Process System',
}


export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html className="h-full bg-gray-50" lang="en">
      <body className={`${inter.className} h-full overflow-y-auto overflow-x-hidden`} >
        <Providers>
          <div className="min-h-screen flex flex-col">
            {children}
          </div>
        </Providers>
      </body>
    </html>
  )
}
