"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic"; // Import next/dynamic
import { signIn } from "@/api/user/userSignIn";
import { saveUser, removeUser } from "@/api/user.localStorage";
import { removeTimer } from "@/api/timer.localStorage";
import LoginComponent from "@/components/login/LoginComponent"; // Import the LoginComponent
import { API_URL } from '@/utils/constants'
import ParticlesBackground from "@/components/ui/Design";
import { AnimatePresence, motion } from "framer-motion";
// Dynamically import the ForgetPassword component
const ForgetPassword = dynamic(() => import("@/components/forgotpassword/ForgetPassword"), {
  ssr: false, // Disable server-side rendering if needed
  loading: () => <p>Loading...</p>, // Optional: show a loading indicator
});

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [userId, setUserId] = useState(0)
  const [useToken, setUseToken] = useState()


  useEffect(() => {

    const fetchData = async () => {
      const url = `${API_URL}client/clients_with_user_id/${userId}`;
      try {
        const response = await fetch(url, {
          method: 'GET', // You can change this to 'POST' or any other method if needed
          headers: {
            'Content-Type': 'application/json',  // assuming the server expects JSON
            'Authorization': `Bearer ${useToken}`,  // Pass the user token in the Authorization header
          },
        });
        const data = await response.json(); // Assuming the response is JSON
        if (Array.isArray(data) && data.length > 0 && data[0] !== "No value found") {
          router.push("/home/<USER>");
        } else {
          // Handle the case where no valid data is found
          
        }

      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [userId, useToken]);  // Make sure to include userToken as a dependency if it changes



  const handleUserNavigation = () => {
    setShowForgotPassword(true);
  };

  const handleCloseProfileModal = () => {
    setShowForgotPassword(false);
  };

  const handleSignIn = async () => {
    const user = await signIn(email, password);
    if (user?.detail) {
      setErrorMessage("Invalid username or password.");
    } else {
      removeTimer()
      removeUser()
      saveUser(user);
      setUserId(user?.user_id)
      setUseToken(user?.token)
      if (user?.roles.some((role) => role.ROLE.includes("ADMIN"))) {
        router.push("/home");
      } else if (user?.roles.some((role) => role.ROLE.includes("USER"))) {
        router.push("/home");
      } else {
        router.push("#");
      }
    }
  };

  return (
    <main className="w-full h-[100vh] bg-gradient-to-b from-violet-900 to-[#21194a] overflow-x-hidden flex flex-col justify-center items-center pb-10 pt-20 lg:p-0">
      <motion.div
        className="z-40 mt-40 lg:mt-10 flex justify-center items-center"
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          duration: 0.8,
          delay: 0.5,
          ease: [0, 0.71, 0.2, 1.01],
        }}>
        <LoginComponent
          email={email}
          setEmail={setEmail}
          password={password}
          setPassword={setPassword}
          handleUserNavigation={handleUserNavigation}
          handleSignIn={handleSignIn}
          errorMessage={errorMessage}
        />
      </motion.div>

      {showForgotPassword && (
        <ForgetPassword isOpen={true} onClose={handleCloseProfileModal} />
      )}
    </main>
  );
}
