"use client";
import UpcomingDetails from "@/components/upcoming/upcomingDetails";  // Assuming the import path is correct
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, ModalTrigger } from "@/components/ui/animated-modal";

export default function Page() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const features = [
    "Automates the interview process by filtering and scoring candidates.",
    "Identifies key skills through customized question sets.",
    "Integrates with your existing hiring pipeline for seamless usage.",
    "Provides detailed candidate profiles with relevant insights.",
    "Reduces bias and promotes fairness in the interview process.",
    "Improves hiring speed and overall team efficiency."
  ];

  const benefits = [
    "Save time by automating candidate evaluations.",
    "Enhance decision-making with data-driven insights.",
    "Focus on the most qualified candidates for final interviews.",
    "Ensure a more objective and consistent evaluation process."
  ];

  return (
    <main className="App flex flex-col w-full h-[100vh] py-32 md:py-10">

      <div className="w-full h-fit lg:px-4 lg:py-20">
        <Heading pgHeading="Upcoming features" textColor="text-slate-200" />
      </div>

      <div className="flex justify-center items-center py-20 lg:py-10 px-4">

        <Modal>
          <ModalTrigger className="bg-black dark:bg-white dark:text-black text-white flex justify-center group/modal-btn">
            <span className="group-hover/modal-btn:translate-x-40 text-center transition duration-500">
              Upcoming Features !
            </span>
            <div className="-translate-x-40 group-hover/modal-btn:translate-x-0 flex items-center justify-center absolute inset-0 transition duration-500 text-white z-20">
              ✈️
            </div>
          </ModalTrigger>

          {/* Modal Body */}
          <ModalBody className="rounded-2xl max-w-[90%]">
            <ModalContent className="relative">
              <h4 className="text-lg md:text-2xl text-neutral-600 dark:text-neutral-100 font-bold text-center mb-8">
                Here are the upcoming {" "}
                <span className="px-1 py-0.5 rounded-md bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 border border-gray-200">
                  features
                </span>{" "}
                ✈️
              </h4>

              <div className="overflow-y-auto max-h-[300px] overflow-x-hidden flex-col flex">

                {/* Features */}
                <div className="mb-6">
                  <h5 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-4">Features:</h5>
                  <ul className="list-disc list-inside space-y-2 text-neutral-700 dark:text-neutral-300">
                    {features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>

                {/* Benefits */}
                <div>
                  <h5 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-4">Benefits:</h5>
                  <ul className="list-disc list-inside space-y-2 text-neutral-700 dark:text-neutral-300">
                    {benefits.map((benefit, index) => (
                      <li key={index}>{benefit}</li>
                    ))}
                  </ul>
                </div>
              </div>



            </ModalContent>
            <ModalFooter className="gap-4">
              <div></div>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </div>
    </main>
  );
}
