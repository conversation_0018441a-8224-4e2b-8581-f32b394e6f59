//Client-rendered
"use client";
import { useState, useEffect } from "react";
import PreHeader from "@/components/ui/PreHeader";
import { TypeAnimation } from 'react-type-animation';
import { AnimatedTestimonials } from "@/components/ui/animated-testimonials";
import { Carousel } from "@/components/ui/carousel";
import { TracingBeam } from "@/components/ui/tracing-beam";
import {
  ArrowDownIcon
} from "@heroicons/react/24/outline";

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';

// import './styles.css';


//Imported
import { motion } from "framer-motion";
import Link from "next/link";
import ParticlesBackground from "@/components/ui/Design";
import Footer from "@/components/ui/Footer";

const scrollToBottom = () => {
  if (typeof window !== 'undefined') {
    window.scrollTo({
      top: document.body.scrollHeight, // Scroll to the bottom of the page
      behavior: 'smooth'               // Smooth scrolling
    });
  }
};

const slideData = [
  {
    title: "Feature 01",
    button: "Explore Feature",
    src: "https://images.unsplash.com/photo-1494806812796-244fe51b774d?q=80&w=3534&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    title: "Feature 02",
    button: "Explore Feature",
    src: "https://images.unsplash.com/photo-1518710843675-2540dd79065c?q=80&w=3387&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    title: "Feature 03",
    button: "Explore Feature",
    src: "https://images.unsplash.com/photo-1590041794748-2d8eb73a571c?q=80&w=3456&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    title: "Feature 04",
    button: "Explore Feature",
    src: "https://images.unsplash.com/photo-1679420437432-80cfbf88986c?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
];

const testimonials = [
  {
    quote:
      "The attention to detail and innovative features have completely transformed our workflow. This is exactly what we've been looking for.",
    name: "Sarah Chen",
    designation: "Product Manager at TechFlow",
    src: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=3560&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    quote:
      "Implementation was seamless and the results exceeded our expectations. The platform's flexibility is remarkable.",
    name: "Michael Rodriguez",
    designation: "CTO at InnovateSphere",
    src: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    quote:
      "This solution has significantly improved our team's productivity. The intuitive interface makes complex tasks simple.",
    name: "Emily Watson",
    designation: "Operations Director at CloudScale",
    src: "https://images.unsplash.com/photo-1623582854588-d60de57fa33f?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    quote:
      "Outstanding support and robust features. It's rare to find a product that delivers on all its promises.",
    name: "James Kim",
    designation: "Engineering Lead at DataPro",
    src: "https://images.unsplash.com/photo-1636041293178-808a6762ab39?q=80&w=3464&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    quote:
      "The scalability and performance have been game-changing for our organization. Highly recommend to any growing business.",
    name: "Lisa Thompson",
    designation: "VP of Technology at FutureNet",
    src: "https://images.unsplash.com/photo-1624561172888-ac93c696e10c?q=80&w=2592&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
];

export default function Home() {
  const [downButtonClicked, setDownButtonClicked] = useState(false)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleScroll = () => {

        if (window.pageYOffset === 0) {
          setDownButtonClicked(false)
        } else {
          setDownButtonClicked(true)
        }
      };

      window.addEventListener('scroll', handleScroll);

      // Cleanup the event listener on component unmount
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, []);

  return (

    <main
      style={{
        backgroundImage: `url(/background.svg), linear-gradient(to bottom, #4d1e94, #0f1729)`,
        backgroundPosition: 'bottom center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain', // Or 'auto', depending on the desired size
      }}
      className="w-full bg-gradient-to-b from-violet-900 via-slate-900 to-slate-900 flex flex-col justify-between overflow-x-hidden overflow-y-hidden">
      <ParticlesBackground
        particleColor="#f2f2f2"
        particleCount={100}
      />
      <PreHeader />
      <TracingBeam className="px-6 ">
        <div className="h-[100vh] w-full">

          {/* Main Content Section */}
          <div className="h-full w-full flex justify-center items-center flex-col py-16">
            <motion.h1
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-[50px] lg:text-[68px] font-extrabold text-center bg-gradient-to-b from-slate-100 to-slate-300 bg-clip-text text-transparent my-8"
              transition={{
                duration: 2.4,
                scale: { type: "spring", visualDuration: 0.4, bounce: 0.5 },
              }}
            >Take the guessing out of <br /> hiring</motion.h1>
            <TypeAnimation
              sequence={[
                " HR-Math.ai is an AI-powered recruitment platform that helps you filter and assess candidates to find the perfect match for every role. Simplify your hiring process with data-driven insights.",
                1000
              ]}
              wrapper="span"
              speed={70}
              style={{ fontSize: '1.2em', display: 'inline-block' }}
              repeat={Infinity}
              className="text-[16px] lg:text-[18px] leading-[26px] max-w-2xl text-center text-slate-400 font-normal mb-8 h-[160px] px-4 lg:p-2"
            />
            <div
              className={`${downButtonClicked ? "hidden" : ""}h-full w-full py-4 px-6 fixed flex justify-center items-end`}>
              <button
                onClick={() => {
                  scrollToBottom();
                  setDownButtonClicked(true);
                }}
                className="">
                <ArrowDownIcon className={`${downButtonClicked ? "hidden" : ""} w-10 h-10 animate-bounce text-slate-200 border-[1px] rounded-full p-[9px] stroke-[3px] bg-slate-800`} />
              </button>
            </div>
          </div>
        </div>

        {/* Features */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, transition: { delay: 0.4, duration: 0.5 } }}
          viewport={{ once: true }}
          className="w-full py-4 px-6 h-fit flex flex-col gap-4">
          <span className="text-[20px] font-bold text-white mb-20">Features</span>
          <div className="w-full lg:h-[360px] px-4 flex items-center relative rounded-xl">

            <Carousel slides={slideData} />
          </div>
        </motion.div>


        {/* Trusted by */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, transition: { delay: 0.4, duration: 0.5 } }}
          viewport={{ once: true }}
          className="w-full px-3 lg:py-4 lg:px-6 h-fit flex flex-col gap-4 mt-40">
          <span className="text-[20px] font-bold text-white">Trusted by</span>
          <div className="w-full lg:h-fit px-10 lg:px-4 bg-white/5 backdrop-blur-sm flex">
            <AnimatedTestimonials testimonials={testimonials} />
          </div>
        </motion.div>
      </TracingBeam>
      <Footer />
    </main>
  );
}
