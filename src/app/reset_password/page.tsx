"use client";
import React, { useState, useEffect } from 'react';
import { useResetPassword } from '@/hooks/user/useResetPassword';

const ResetPassword = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [email, setEmail] = useState('');
  const [token, setToken] = useState('');
  const [buttonText, setButtonText] = useState('Reset Password');

  // Extract email and token from URL parameters on component mount
  useEffect(() => {
    // Ensure this code only runs in the browser
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const extractedToken = urlParams.get('token');
      const extractedEmail = urlParams.get('email');
      if (extractedToken) setToken(extractedToken);
      if (extractedEmail) setEmail(extractedEmail);
    }
  }, []);

  // Using the custom hook to handle reset password
  const { mutate, isLoading, isSuccess, isError, data } = useResetPassword(password, token);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (password === confirmPassword) {
      mutate();
     
    } else {
      setButtonText("Passwords don't match");
      setTimeout(() => {
        setButtonText("Reset Password");
      }, 3000);
    }
  };

  // Update button text with backend response if success
  useEffect(() => {
    if (isSuccess && data) {
      setButtonText(JSON.parse(data.message.replace(/'/g, '"').slice(0, -1)).msg || "Password Reset Successfully22");
    }
  }, [isSuccess, data]);

  return (
    <main className='w-full h-[100vh] bg-gradient-to-b from-violet-900 to-[#21194a] overflow-x-hidden flex flex-col justify-center items-center pb-10 pt-20 lg:p-0'>
      <div className='flex justify-center items-center text-4xl md:text-8xl mb-6'>
        <span className='text-slate-600'>.ai</span>
      </div>
      <div className='bg-white p-6 md:p-8 rounded shadow-lg w-full max-w-md'>
        <h1 className='text-xl md:text-2xl font-bold mb-6 text-center'>Reset Password</h1>
        <form onSubmit={handleSubmit}>
          <div className='mb-4'>
            <label className='block text-gray-700 text-sm font-bold mb-2' htmlFor='new-password'>
              New Password
            </label>
            <input
              type='password'
              id='new-password'
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <div className='mb-6'>
            <label className='block text-gray-700 text-sm font-bold mb-2' htmlFor='confirm-password'>
              Confirm Password
            </label>
            <input
              type='password'
              id='confirm-password'
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </div>

          {/* Display button text based on mutation status */}
          <button
            type="submit"
            disabled={isSuccess} // Correctly set the disabled attribute
            className="w-full bg-secondary text-white py-2 rounded-md hover:bg-hoverColor transition-colors"
          >
            {isLoading ? "Loading..." : buttonText}
          </button>

        </form>

        {/* Error handling */}
        {isError && <div className="text-red-500 mt-4">Failed to reset password. Please try again.</div>}
      </div>
    </main>
  );
};

export default ResetPassword;
