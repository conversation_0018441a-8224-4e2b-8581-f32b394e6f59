import React from 'react';

interface LandingPageCardProp {
    image: any;
    heading: string;
    text: string;
}

const FeatureCards = ({ image, heading, text }: LandingPageCardProp) => {
    const defaultImage = '/empty.png';
    const defaultHeading = 'Default Heading';
    const defaultText = 'This is some default text.';

    return (
        <div className="w-[300px] lg:w-[400px] h-[300px] rounded-lg bg-white flex-shrink-0">
            <div>
                <img
                    src={image || defaultImage}
                    alt="Card"
                    className="w-full h-[200px] object-cover rounded-t-lg"
                />
            </div>
            <div className="h-[100px] w-full p-4 bg-[#0e102e]">
                <h1 className="text-xl font-semibold text-slate-100">{heading || defaultHeading}</h1>
                <p className='text-slate-100'>{text || defaultText}</p>
            </div>
        </div>
    );
};


export default FeatureCards;
