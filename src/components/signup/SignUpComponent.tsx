import Link from "next/link";
import React from "react";
import Image from 'next/image';
import SubmitTypeButton from "../ui/SubmitTypeButton";

interface SignUpComponentProps {
    email: string;
    setEmail: React.Dispatch<React.SetStateAction<string>>;
    password: string;
    setPassword: React.Dispatch<React.SetStateAction<string>>;
    confirmPassword: string;
    setConfirmPassword: React.Dispatch<React.SetStateAction<string>>;
    handleSignUp: () => void;
    errorMessage?: string;
}

const SignUpComponent: React.FC<SignUpComponentProps> = ({
    email,
    setEmail,
    password,
    setPassword,
    confirmPassword,
    setConfirmPassword,
    handleSignUp,
    errorMessage
}) => {
    return (
        <div className="w-10/12 lg:w-[900px] bg-white h-fit flex flex-col lg:flex-row shadow-2xl rounded-2xl p-8 gap-10 justify-between items-center"
            style={{ backgroundImage: `url(/signup/signup_background.svg)` }}>
            {/* Image on the left */}
            <div className='h-full w-full flex-1'>
                <Image src="/signup/signup.svg" alt="Signup" width={500} height={500} />
            </div>

            <div className="h-full w-full flex-1 p-4">
                <h2 className="mb-10 text-center text-2xl font-bold leading-9 tracking-tight text-textPrimary">
                    Create an Account
                </h2>
                <div className="space-y-6">
                    {/* Email Input */}
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium leading-6 text-textColor">
                            Email address
                        </label>
                        <div className="mt-2">
                            <input
                                id="email"
                                name="email"
                                type="email"
                                placeholder="Enter your email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                autoComplete="email"
                                required
                                className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                    </div>

                    {/* Password Input */}
                    <div>
                        <label htmlFor="password" className="block text-sm font-medium leading-6 text-textColor">
                            Password
                        </label>
                        <div className="mt-2">
                            <input
                                id="password"
                                name="password"
                                type="password"
                                placeholder="Enter your password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                autoComplete="new-password"
                                required
                                className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                    </div>

                    {/* Confirm Password Input */}
                    <div>
                        <label htmlFor="confirmPassword" className="block text-sm font-medium leading-6 text-textColor">
                            Confirm Password
                        </label>
                        <div className="mt-2">
                            <input
                                id="confirmPassword"
                                name="confirmPassword"
                                type="password"
                                placeholder="Confirm your password"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                autoComplete="new-password"
                                required
                                className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                    </div>

                    {/* Error Message */}
                    {errorMessage && (
                        <div className="flex justify-center text-sm text-red-500">
                            {errorMessage}
                        </div>
                    )}

                    {/* Sign Up Button */}
                    <div>
                        <SubmitTypeButton onClick={handleSignUp}>
                            Sign up &rarr;
                        </SubmitTypeButton>
                    </div>
                </div>

                {/* Login Link */}
                <p className="mt-10 text-center text-sm text-gray-500">
                    Already a member?{" "}
                    <Link
                        href="/login"
                        className="font-semibold leading-6 bg-gradient-to-b from-[#21184a] to-[#0f1729] bg-clip-text text-transparent"
                    >
                        Log in here
                    </Link>
                </p>
            </div>
        </div>
    );
};

export default SignUpComponent;
