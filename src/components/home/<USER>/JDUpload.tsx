"use client";
import React, { useEffect, useState } from "react";
import {
  CloudArrowUpIcon,
} from "@heroicons/react/20/solid";
import {
  CheckBadgeIcon,
  InformationCircleIcon,
  XCircleIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";
import { usePostUploadJD } from "@/hooks/home/<USER>/usePostUploadJD";
import { useRemoveUploadedJD } from "@/hooks/home/<USER>/jd/useRemoveUploadedJD";

interface JDUploadProps {
  handleUploadJD: (e: ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>) => void;
  fileNameJD: string;
  handleRemoveJD: () => void;
  selectedRunId?: number;
  setSelectedTab?: any;
}

export default function JDUpload({ fileNameJD, selectedRunId, setSelectedTab }: JDUploadProps) {
  const [fileNameJDDetails, setFileNameJDDetails] = useState(fileNameJD);
  const [file, setFile] = useState<File | undefined>(undefined);
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");

  const deletejd = useRemoveUploadedJD(selectedRunId)
  const postjd = usePostUploadJD(selectedRunId, file);

  const handleRemoveJD = async () => {
    //Maybe in future we will need it
    console.log("jdfile removing")
    try {
      const response = await deletejd.mutateAsync(selectedRunId);
      console.log("Response Message:", response);

      if (response.msg) {
        setFile(null)
        setFileNameJDDetails("")
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        // setFileNameCV("");  // Reset file name if a message is received
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  const handleUploadJD = async (e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>) => {
    // Prevent the default form submission behavior
    e.preventDefault();
    console.log("jdfile uploading")


    let uploadedFile: File | null = null;

    if ('dataTransfer' in e && e.dataTransfer?.files) {
      // For drag-and-drop, use e.dataTransfer.files
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      // For file input change event, use e.target.files
      uploadedFile = e.target.files[0];
    }


    // Retrieve the file from the event
    setFile(uploadedFile)
    // const file = e.target.files[0];

    if (!(uploadedFile)) {
      setModalType("err")
      setModalMessage("No JD file found")
      setFile(undefined)
      return;
    }

    // Check if the file is a valid format (for example, .pdf or .docx)
    const validTypes = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"];
    if (!validTypes.includes((uploadedFile).type)) {
      setModalType("err")
      setModalMessage("Please upload a valid file (PDF or DOCX)")
      return;
    }

    // Optionally, you can display the file name or other information
    console.log(`File selected: ${(uploadedFile).name}`);
    console.log("File inside", file)
    // setFileNameJD(`${file.name}`)


    try {
      // Trigger the mutation to upload the file
      const response = await postjd.mutateAsync(selectedRunId, file);

      if (response.msg) {
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        setFileNameJDDetails(`${(uploadedFile).name}`)
        setFile(undefined)
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }


      // Check if the response is an array and contains the message
      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];  // Access the first item in the array
        console.log("Response Message:", message);  // Log the message
        // alert("Response Message: " + message);  // Show in alert
      } else {
        console.error("Response is empty or not an array");
        // alert("Unexpected response format.");
      }

    } catch (error) {
      console.error("Error uploading JD file:", error);
      // alert("An error occurred while uploading the file. Please try again.");
    }
  }

  return (
    <div className="flex justify-center items-center h-full w-full p-4">
      <div className="w-full max-w-3xl">
        {/* Header Section */}
        <div className="text-center mb-4">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full mb-2">
            <CloudArrowUpIcon className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-1">
            Upload Job Description
          </h2>
          <p className="text-sm text-gray-600 max-w-lg mx-auto">
            Upload the job description document for candidate matching
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Upload Section */}
          <div className="lg:col-span-2 space-y-4">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-50 to-transparent rounded-full -translate-y-12 translate-x-12"></div>

              <div className="relative">
                <div className="flex items-center gap-2 mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Job Description Document</h3>
                  <div className="relative group">
                    <InformationCircleIcon className="w-4 h-4 text-gray-500" />
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg whitespace-nowrap z-10">
                      Upload a comprehensive job description
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div
                    className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 ${
                      fileNameJDDetails
                        ? "border-green-300 bg-green-50"
                        : "border-gray-300 hover:border-blue-400 hover:bg-blue-50"
                    }`}
                    onDrop={(e) => handleUploadJD(e)}
                    onDragOver={(e) => e.preventDefault()}
                  >
                    {fileNameJDDetails ? (
                      <div className="flex flex-col items-center gap-3">
                        <div className="rounded-full bg-green-100 p-2">
                          <CheckBadgeIcon className="w-6 h-6 text-green-600" />
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-semibold text-green-800 mb-1">
                            Job Description Uploaded Successfully!
                          </div>
                          <div className="text-xs text-green-700 bg-green-100 px-3 py-1 rounded-full inline-block mb-2">
                            {fileNameJDDetails}
                          </div>
                          <div className="flex justify-center">
                            <button
                              onClick={handleRemoveJD}
                              className="text-xs text-red-600 hover:text-red-800 underline transition-colors"
                            >
                              Remove and upload different file
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center gap-4">
                        <div className="rounded-full bg-blue-50 p-3">
                          <CloudArrowUpIcon className="w-8 h-8 text-blue-600" />
                        </div>
                        <div className="text-center">
                          <p className="text-sm font-semibold text-gray-900 mb-1">
                            Drag & drop your job description here
                          </p>
                          <p className="text-xs text-gray-600 mb-3">
                            or click to select from your computer
                          </p>
                          <input
                            type="file"
                            id="fileJD"
                            className="hidden"
                            onChange={(e) => handleUploadJD(e)}
                            accept=".pdf,.docx"
                          />
                          <label
                            htmlFor="fileJD"
                            className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium rounded-lg cursor-pointer transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
                          >
                            Choose File
                          </label>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span>Supports PDF, DOCX</span>
                          <span>•</span>
                          <span>Max size: 10MB</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Action Button */}
            <div className="flex justify-center">
              <button
                onClick={() => setSelectedTab(2)}
                disabled={!fileNameJDDetails}
                className={`py-3 px-6 text-sm font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 ${
                  fileNameJDDetails
                    ? "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white transform hover:scale-[1.01]"
                    : "bg-gray-100 text-gray-400 cursor-not-allowed"
                }`}
              >
                Continue to CV Upload <ArrowRightIcon className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Info Panel */}
          <div className="space-y-4">
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
              <h4 className="text-sm font-semibold text-blue-900 mb-3">Best Practices</h4>
              <div className="space-y-2 text-xs text-blue-800">
                <div className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
                  <p>Include detailed role responsibilities and requirements</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
                  <p>Specify required skills, experience levels, and qualifications</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
                  <p>Use clear, structured formatting for better analysis</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">What happens next?</h4>
              <div className="space-y-2 text-xs text-gray-700">
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold">1</div>
                  <p>Upload candidate CVs (single or multiple)</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold">2</div>
                  <p>Configure assessment parameters</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold">3</div>
                  <p>Generate comprehensive talent reports</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}