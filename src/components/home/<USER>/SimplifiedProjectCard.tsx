"use client"
import React, { useState } from 'react'
import AskModal from '@/components/ui/extra/AskModal';
import { FolderIcon, UserIcon, CalendarDaysIcon, PlayIcon, PlusIcon } from "@heroicons/react/24/outline";
import { useRemoveProject } from '@/hooks/home/<USER>/useRemoveProject';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

// Define the interface for the props
interface ProjectDetails {
  project_id: number;
  project_name: string;
  project_description: string;
  creation_date: string
  selectedClientId: number;
  client_name: string;
  run_is_present: boolean;
}

const SimplifiedProjectCard: React.FC<ProjectDetails> = ({
  project_id,
  project_name,
  project_description,
  client_name,
  creation_date,
  run_is_present,
  selectedClientId
}) => {

  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const deleteProject = useRemoveProject(project_id)

  const handleDeleteProject = async () => {
    const response = await deleteProject.mutateAsync()

    if (response.msg) {
      // Success
    }
    else if (response.err) {
      // Error handling
    }
  }

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const handleRunListClick = () => {
    window.location.href = `/home/<USER>/projectlist/${project_id}`;
  };

  const handleCreateRunClick = () => {
    window.location.href = `/home/<USER>
  };

  return (
    <div>
      {showDeleteModal && (
        <AskModal
          questionToAsk="Do you want to continue?"
          questionDetail="Deleting the project cannot be undone."
          onYes={handleDeleteProject}
          onNo={closeDeleteModal}
          closeModal={closeDeleteModal}
        />
      )}

      <Card className="overflow-hidden h-[400px] flex flex-col" shadow="sm">
        {/* Static Header with primary background */}
        <CardHeader className="bg-primary-50 border-b border-primary-100 flex-shrink-0">
          <div className="flex items-center justify-between mb-2">
            <FolderIcon className="h-8 w-8 text-primary-600" />
            {run_is_present && (
              <span className="bg-success-100 text-success-800 text-xs px-3 py-1 rounded-full font-medium">
                Has Runs
              </span>
            )}
          </div>
          <CardTitle className="text-lg mb-1">{project_name}</CardTitle>
          <div className="flex items-center justify-between">
            <p className="text-xs text-gray-600 font-medium">Project ID: {project_id}</p>
            <span className="text-xs text-primary-700 bg-primary-100 px-2 py-1 rounded-full font-medium">AI Interview</span>
          </div>
        </CardHeader>

        {/* Compact Content */}
        <CardContent className="py-3 flex-grow flex flex-col justify-between">
          <div className="mb-3">
            <p className="text-sm text-gray-700 leading-tight line-clamp-3">{project_description}</p>
          </div>

          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <span className="text-gray-500 font-medium">Client:</span>
              <div className="flex items-center mt-1">
                <UserIcon className="h-3 w-3 mr-1 text-gray-400" />
                <span className="text-gray-700 font-medium truncate">{client_name}</span>
              </div>
            </div>
            <div>
              <span className="text-gray-500 font-medium">Created:</span>
              <div className="flex items-center mt-1">
                <CalendarDaysIcon className="h-3 w-3 mr-1 text-gray-400" />
                <span className="text-gray-700 font-medium">{new Date(creation_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: '2-digit' })}</span>
              </div>
            </div>
          </div>
        </CardContent>

        {/* Actions - Fixed at bottom */}
        <div className="flex-shrink-0 px-4 pb-4 pt-2 border-t border-gray-100">
          <div className="space-y-1.5">
            <Button
              onClick={handleRunListClick}
              disabled={!run_is_present}
              variant={run_is_present ? "primary" : "secondary"}
              size="sm"
              fullWidth
              icon={<PlayIcon className="h-3 w-3" />}
              className="text-xs"
            >
              View Run List
            </Button>

            <Button
              onClick={handleCreateRunClick}
              variant="secondary"
              size="sm"
              fullWidth
              icon={<PlusIcon className="h-3 w-3" />}
              className="text-xs"
            >
              Create New Run
            </Button>

            <Button
              onClick={() => setShowDeleteModal(true)}
              variant="error"
              size="sm"
              fullWidth
              className="text-xs"
            >
              Delete Project
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default SimplifiedProjectCard;
