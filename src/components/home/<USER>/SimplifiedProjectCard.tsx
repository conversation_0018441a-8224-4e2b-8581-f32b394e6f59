"use client"
import React, { useState } from 'react'
import AskModal from '@/components/ui/extra/AskModal';
import { FolderIcon, UserIcon, CalendarDaysIcon, PlayIcon, PlusIcon } from "@heroicons/react/24/outline";
import { useRemoveProject } from '@/hooks/home/<USER>/useRemoveProject';

// Define the interface for the props
interface ProjectDetails {
  project_id: number;
  project_name: string;
  project_description: string;
  creation_date: string
  selectedClientId: number;
  client_name: string;
  run_is_present: boolean;
}

const SimplifiedProjectCard: React.FC<ProjectDetails> = ({
  project_id,
  project_name,
  project_description,
  client_name,
  creation_date,
  run_is_present,
  selectedClientId
}) => {

  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const deleteProject = useRemoveProject(project_id)

  const handleDeleteProject = async () => {
    const response = await deleteProject.mutateAsync()

    if (response.msg) {
      // Success
    }
    else if (response.err) {
      // Error handling
    }
  }

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const handleRunListClick = () => {
    window.location.href = `/home/<USER>/projectlist/${project_id}`;
  };

  const handleCreateRunClick = () => {
    window.location.href = `/home/<USER>
  };

  return (
    <div>
      {showDeleteModal && (
        <AskModal
          questionToAsk="Do you want to continue?"
          questionDetail="Deleting the project cannot be undone."
          onYes={handleDeleteProject}
          onNo={closeDeleteModal}
          closeModal={closeDeleteModal}
        />
      )}

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-[400px] flex flex-col">
        {/* Static Header with light blue background */}
        <div className="bg-blue-50 p-4 border-b border-blue-100 flex-shrink-0">
          <div className="flex items-center justify-between mb-2">
            <FolderIcon className="h-8 w-8 text-blue-600" />
            {run_is_present && (
              <span className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">
                Has Runs
              </span>
            )}
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-1">{project_name}</h3>
          <div className="flex items-center justify-between">
            <p className="text-xs text-gray-600 font-medium">Project ID: {project_id}</p>
            <span className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-full font-medium">AI Interview</span>
          </div>
        </div>

        {/* Compact Content */}
        <div className="px-4 py-3 flex-grow flex flex-col justify-between">
          <div className="mb-3">
            <p className="text-sm text-gray-700 leading-tight line-clamp-3">{project_description}</p>
          </div>

          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <span className="text-gray-500 font-medium">Client:</span>
              <div className="flex items-center mt-1">
                <UserIcon className="h-3 w-3 mr-1 text-gray-400" />
                <span className="text-gray-700 font-medium truncate">{client_name}</span>
              </div>
            </div>
            <div>
              <span className="text-gray-500 font-medium">Created:</span>
              <div className="flex items-center mt-1">
                <CalendarDaysIcon className="h-3 w-3 mr-1 text-gray-400" />
                <span className="text-gray-700 font-medium">{new Date(creation_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: '2-digit' })}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Actions - Fixed at bottom */}
        <div className="flex-shrink-0 px-4 pb-4 pt-2 border-t border-gray-100">
          <div className="space-y-1.5">
            <button
              onClick={handleRunListClick}
              disabled={!run_is_present}
              className={`w-full py-2 px-3 rounded-md font-medium text-xs flex items-center justify-center gap-2 transition-colors duration-200 ${
                run_is_present
                  ? "bg-blue-100 text-blue-800 hover:bg-blue-200"
                  : "bg-gray-100 text-gray-400 cursor-not-allowed"
              }`}
            >
              <PlayIcon className="h-3 w-3" />
              View Run List
            </button>

            <button
              onClick={handleCreateRunClick}
              className="w-full py-2 px-3 rounded-md font-medium text-xs flex items-center justify-center gap-2 bg-slate-100 text-slate-700 hover:bg-slate-200 transition-colors duration-200"
            >
              <PlusIcon className="h-3 w-3" />
              Create New Run
            </button>

            <button
              onClick={() => setShowDeleteModal(true)}
              className="w-full py-2 px-3 rounded-md font-medium text-xs flex items-center justify-center gap-2 bg-red-100 text-red-700 hover:bg-red-200 transition-colors duration-200"
            >
              Delete Project
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SimplifiedProjectCard;
