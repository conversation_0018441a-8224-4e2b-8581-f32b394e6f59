import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query'
import Image from 'next/image';
import { useGetLogo } from '@/hooks/home/<USER>/useGetLogo';
import { useEditUserDetails } from '@/hooks/user/useEditUserDetails';
import { useEditUserLogo } from '@/hooks/user/useEditUserLogo';
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { useGetUserLogo } from '@/hooks/user/useGetUserLogo'
import { ArrowPathIcon, DivideIcon, EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

const MainScreen = ({ userDataInfo, setshowImage, setImageData, activeState, setModalType, setModalMessage, user }) => {
    const queryClient = useQueryClient()
    const [clientLogo, setClientLogo] = useState('/logo_here.png');
    const [file, setFile] = useState<File>(null);
    const [isLoading, setIsLoading] = useState(false)
    const [imagePreview, setImagePreview] = useState<string | null>()
    const [userFullName, setUserFullName] = useState(userDataInfo?.user_full_name)
    const [userEmail, setUserEmail] = useState(userDataInfo?.user_email)
    const [userPhone, setUserPhone] = useState(userDataInfo?.user_phone)
    const [userPassword, setUserPassword] = useState(userDataInfo?.password)
    const [passwordType, setPasswordType] = useState(true)
    const [userCreationDate, setUserCreationDate] = useState(userDataInfo?.creation_date)
    const [userId, setuserId] = useState(userDataInfo?.user_id)
    const { data: userLogoData } = useGetUserLogo(userDataInfo?.user_logo)
    console.log("userLogoData", userLogoData)
    const [userLogo, setuserLogo] = useState<any>()
    const [userLogoFile, setuserLogoFile] = useState<File>()

    console.log("userLogo", userLogo)
    console.log("userLogoFile", userLogoFile)

    if (!userDataInfo) {
        return <div>No User info available</div>;
    }

    // Destructure client info for cleaner code
    const { logo, user_id, client_id, name: clientNameFromInfo, description } = userDataInfo;
    const { data: clientLogoData } = useGetLogo(logo);

    // Function to handle invalid file type
    const handleInvalidFileType = () => {
        alert('Please select a valid image file (JPEG, PNG, GIF).');
        // You can replace the alert with any other UI action or validation message.
    };


    const handlePhotoFileChange = (e) => {
        const selectedFile = e.target.files ? e.target.files[0] : null;

        // Check if a file is selected
        if (selectedFile) {
            // Validate if the selected file is an image (jpeg, png, gif, etc.)
            const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (validImageTypes.includes(selectedFile.type)) {
                // If it's a valid image, proceed
                setuserLogoFile(selectedFile); // Store the file
                setuserLogo(URL.createObjectURL(selectedFile)); // Create a URL for the image
            } else {
                // If the file is not an image, call a function to handle invalid file type
                handleInvalidFileType(); // Call the function when the file is not a valid image
            }
        }
    };





    // Call the editClient mutation with appropriate parameters
    const editUserLogo = useEditUserLogo(userId, userLogoFile);
    const editUserDetails = useEditUserDetails(userId, userEmail, userPassword, userPhone, userFullName);

    const urlToFile = async (url, filename, mimeType) => {
        const response = await fetch(url);
        const data = await response.blob();
        return new File([data], filename, { type: mimeType });
    };

    const handleUploadOnlyUserLogo = async () => {
        setIsLoading(true);

        try {
            const formData = new FormData();

            let finalFile = userLogoFile;
            if (!finalFile && userLogo !== '/user.png') {
                // If no file has been uploaded, use the existing logo if it's not the default user image
                const currentImageFile = await urlToFile(userLogo, 'current_logo.png', 'image/png');
                finalFile = currentImageFile;
            }

            if (finalFile) {
                formData.append('file', finalFile); // Append the file
            }

            const response = await editUserLogo.mutateAsync(formData);
            if (response.msg) {
                setModalType("msg"); // Set modal type to "msg"
                setModalMessage(response.msg); // Set the message from the response
            } else if (response.err) {
                setModalType("err"); // Set modal type to "err"
                setModalMessage(response.err);
            } else if (response.detail) {
                setModalType("err"); // Set modal type to "err" for details
                setModalMessage(response.detail);
            }
            console.log("Logo updated successfully:", response);
        } catch (error) {
            console.error('Error updating user logo:', error);
            alert('Error saving user logo.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmitUserDetails = async (e) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            const formData = new FormData();

            // Append the user details
            formData.append('user_full_name', userFullName);
            formData.append('user_email', userEmail);
            formData.append('user_phone', userPhone);

            const response = await editUserDetails.mutateAsync(formData);
            console.log('User details updated successfully:', response);

            if (response.msg) {
                setModalType("msg"); // Set modal type to "msg"
                setModalMessage(response.msg); // Set the message from the response
            } else if (response.err) {
                setModalType("err"); // Set modal type to "err"
                setModalMessage(response.err);
            } else if (response.detail) {
                setModalType("err"); // Set modal type to "err" for details
                setModalMessage(response.detail);
            }

            setFile(null); // Reset the file state after submission
        } catch (error) {
            console.error('Error updating user details:', error);
            alert('Error saving user details.');
        } finally {
            setIsLoading(false);
        }
    };


    console.log("clientLogo", clientLogo)
    if (activeState == 0) {
        return (
            <div className="w-[90%] lg:w-full flex-auto bg-white m-6 p-4 h-fit lg:h-[90%] flex-row shadow-xl rounded-xl text-2xl text-slate-500">
                <div className="flex flex-col lg:flex-row items-center h-full w-full gap-4">

                    <div className="border w-full h-full flex flex-col justify-around  items-center bg-gradient-to-tl from-slate-50 to-slate-100 rounded-md">
                        <div className='flex flex-col justify-between lg:justify-around gap-[40px] w-full p-2 md:px-10 md:py-4'>

                            {/* Top section */}
                            <div className='w-full flex flex-col lg:flex-row justify-center gap-10 md:gap-40 items-center h-full p-4 lg:px-20 shadow-md'>
                                <div className='flex flex-col lg:flex-row w-fit justify-center items-center gap-8'> 
                                    <div className='flex flex-col gap-4'>
                                        <img
                                            className='w-[200px] h-[200px] p-[4px] border-2 rounded-full'
                                            height={100}
                                            width={100}
                                            src={userLogo || (userDataInfo?.user_logo == null ? '/user.png' : userLogoData)}
                                            alt="User Logo" />
                                        <div className="flex flex-col gap-4 lg:gap-20 p-2 text-base justify-center items-center w-full">
                                            <div className='flex gap-10'>
                                                {!userLogo ? (
                                                    <div className='flex gap-10'>
                                                    </div>
                                                )
                                                    :
                                                    <div className='flex gap-10'>
                                                        <motion.button
                                                            whileHover={{ scale: 1.1 }}
                                                            whileTap={{ scale: 0.8 }}
                                                            className="text-sm text-white bg-secondary py-[6px] px-[8px] rounded-md hover:bg-red-400 transition delay-75"
                                                            onClick={() => {
                                                                setuserLogo(null); setuserLogoFile(null)
                                                            }}
                                                        >
                                                            Cancel
                                                        </motion.button>

                                                        <motion.button
                                                            whileHover={{ scale: 1.1 }}
                                                            whileTap={{ scale: 0.8 }}
                                                            className="text-sm text-white bg-secondary py-[6px] px-[8px] rounded-md hover:bg-green-400 transition delay-75"
                                                            onClick={() => {
                                                                handleUploadOnlyUserLogo();
                                                                setuserLogo(null);
                                                            }}
                                                        >
                                                            Save Image
                                                        </motion.button>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>

                                    <div className='flex flex-col'>
                                        <span className='text-[20px]'>{userFullName || "Enter user name"}</span>
                                        <span className='text-[14px]'>{userEmail}</span>
                                    </div>
                                </div>
                                <div className='h-full w-fit flex justify-center items-center'>
                                    <motion.button
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.8 }} className="p-[3px] relative h-fit"
                                        onClick={() => document.getElementById('file-upload').click()} // Trigger file input click
                                    >
                                        <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg" />
                                        <div className="px-8 py-2 bg-slate-200 rounded-[6px] relative group transition duration-200 text-slate-800 hover:bg-transparent hover:text-slate-200 text-[16px]">
                                            Choose File
                                            <input
                                                id="file-upload"
                                                className="hidden"
                                                type="file"
                                                onChange={(e) => { handlePhotoFileChange(e); }}
                                            />
                                        </div>
                                    </motion.button>
                                </div>

                            </div>

                            {/* Personal Info */}
                            <div className='w-full h-fit flex flex-col lg:flex-row gap-6 justify-between lg:px-20'>
                                {/* Left section */}
                                <div>
                                    <h1 className='text-[20px]'>Personal Info</h1>
                                    <h1 className='text-[14px]'>Enter your personal Info</h1>
                                </div>

                                {/* Right section */}
                                <div className="text-base h-fit lg:text-lg xl:text-xl w-full lg:w-fit p-4 shadow-md lg:p-8 flex flex-col gap-10 sm:gap-4 lg:gap-6 xl:gap-10 bg-white rounded-xl">
                                    <div className="flex flex-col lg:flex-row justify-between gap-4">
                                        <span>User ID:</span>
                                        <span className="font-semibold">
                                            <input
                                                className="rounded-lg"
                                                value={userId}
                                                type="number"
                                                name="name"
                                                placeholder="User ID here"
                                                disabled
                                            />
                                        </span>
                                    </div>
                                    <div className="flex flex-col lg:flex-row justify-between gap-4">
                                        <span>User Full Name:</span>
                                        <span className="font-semibold">
                                            <input
                                                className="rounded-lg "
                                                type="text"
                                                name="name"
                                                placeholder="Enter User Full Name"
                                                value={userFullName}
                                                onChange={(e) => setUserFullName(e.target.value)}

                                            />
                                        </span>
                                    </div>
                                    <div className="flex flex-col lg:flex-row justify-between gap-4">
                                        <span>User Email:</span>
                                        <span className="font-semibold">
                                            <input
                                                className="rounded-lg "
                                                type="email"
                                                name="name"
                                                value={userEmail}
                                                placeholder="Enter Email Address"
                                                onChange={(e) => setUserEmail(e.target.value)}
                                            />
                                        </span>
                                    </div>
                                    <div className="flex flex-col lg:flex-row justify-between gap-4">
                                        <span>User Phone:</span>
                                        <span className="font-semibold">
                                            <input
                                                className="rounded-lg "
                                                type="tel"
                                                value={userPhone}
                                                name="name"
                                                placeholder="Enter User Phone Number"
                                                onChange={(e) => setUserPhone(e.target.value)}
                                            />
                                        </span>
                                    </div>


                                    <div className="flex flex-col lg:flex-row justify-between gap-4">
                                        <span>Creation date:</span>
                                        <span className="font-semibold">
                                            <input
                                                className="rounded-lg "
                                                type="text"
                                                name="name"
                                                placeholder={new Date(userCreationDate).toLocaleString("en-US", {

                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',

                                                })}
                                                disabled
                                            />
                                        </span>
                                    </div>
                                    <div className='w-full flex justify-center -mt-4'>
                                        <motion.button whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.8 }}
                                            className="h-fit w-fit mt-8 lg:-mt-2 mb-2 bg-secondary text-lg text-white p-2 rounded-lg flex gap-2"
                                            onClick={handleSubmitUserDetails} // Updated handleSubmit function
                                        >
                                            {/* <div className={`${isLoading && "animate-spin"}`}>
                                                <ArrowPathIcon className="h-[25px] w-[25px]" />
                                            </div> */}
                                            Save
                                        </motion.button>
                                    </div>

                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        );

    }

};

export default MainScreen;
