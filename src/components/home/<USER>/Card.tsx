import React, { useState } from 'react'
import { cn } from "@/lib/utils";
import { AnimatePresence } from 'framer-motion';
import { CanvasRevealEffect } from '@/components/ui/canvas-reveal-effect';

const Card = ({
    JD_Requirement,
    Candidate_Name,
    Final_Rank,
    Final_Score,
    Similarity_Score,
    Consistency_Score,
    Reasoning_for_Similarity_Score,
    Reasoning_for_Consistency_Score
}) => {

    const [activeButton, setActiveButton] = useState('consistency');
    const [hovered, setHovered] = useState(false)

    return (
        <div
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
            className='rounded-2xl p-4 overflow-hidden bg-black/60 backdrop-blur-sm border border-transparent dark:border-white/[0.2] group-hover:border-slate-700 relative z-20 w-fit sm:w-[600px] lg:w-fit h-fit flex flex-col justify-between shadow-md gap-10 m-4 text-slate-200'>
            {hovered && <AnimatePresence>
                <div className="h-full w-full absolute inset-0">
                    <CanvasRevealEffect
                        animationSpeed={3}
                        containerClassName="bg-transparent"
                        colors={[
                            [236, 72, 153],
                            [232, 121, 249],
                        ]}
                        dotSize={2}
                    />
                </div>
            </AnimatePresence>}
            <div className='flex gap-4 md:gap-10 lg:gap-20 xl:gap-0 xl:justify-between z-50'>
                <div className='flex flex-col gap-2'>
                    <div className="text-sm flex justify-center items-center bg-gray-700 text-white font-medium rounded-md lg:rounded-full md:px-2  md:py-0 h-fit w-fit">
                        <p className="p-3">JD Requirement: {JD_Requirement} </p>
                    </div>
                    <div className=''>
                        Candidate Name:<span className='font-bold'> {Candidate_Name}</span>
                    </div>
                </div>
                <div className='flex justify-between gap-4 md:gap-20 text-sm md:text-md lg:text-lg'>
                    <div className='flex flex-col justify-center items-center'>
                        <span className='font-semibold'>{Final_Rank}</span>
                        <span>Final Rank</span>
                    </div>
                    <div className='flex flex-col justify-center items-center'>
                        <span className='font-semibold'>{Final_Score}</span>
                        <span>Final Score</span>
                    </div>
                </div>
            </div>
            <div className='flex flex-col z-50'>
                <div className='h-fit lg:h-[30px] w-full flex flex-row'>
                    <button
                        className={`border-[1px] rounded-tl-lg py-1 px-2 ${activeButton === 'consistency' ? 'bg-secondary text-white w-fit h-fit' : ''}`}
                        onClick={() => setActiveButton('consistency')}
                    >
                        Consistency Score
                    </button>
                    <button
                        className={`border-[1px] rounded-tr-lg py-1 px-2 ${activeButton === 'similarity' ? 'bg-secondary text-white w-fit h-fit' : ''}`}
                        onClick={() => setActiveButton('similarity')}
                    >
                        Similarity Score
                    </button>
                </div>
                {activeButton === "consistency"
                    ?
                    <div className='flex flex-col p-2 border-x border-b gap-2'>
                        <div className="text-md flex border border-secondary rounded-lg px-2 py-2 w-fit h-fit">
                            <p className="px-1">
                                Consistency Score: {Consistency_Score}
                            </p>
                        </div>
                        <div className='flex flex-col gap-2'>
                            <span className='font-semibold'>
                                Reasoning for Consistency Score:
                            </span>
                            <p>
                                {Reasoning_for_Consistency_Score}
                            </p>
                        </div>
                    </div>
                    : <div className='flex flex-col p-2 border-x border-b gap-2'>
                        <div className="text-md flex border border-secondary rounded-lg px-2 py-2 w-fit h-fit">
                            <p className="px-1">
                                Similarity Score: {Similarity_Score}
                            </p>
                        </div>
                        <div className='flex flex-col gap-2'>
                            <span className='font-semibold'>
                                Reasoning for Similarity Score:
                            </span>
                            <p className=''>
                                {Reasoning_for_Similarity_Score}
                            </p>
                        </div>
                    </div>
                }

            </div>
        </div>
    )
}

export default Card;


export const CardTitle = ({
    className,
    children,
}: {
    className?: string;
    children: React.ReactNode;
}) => {
    return (
        <h4 className={cn("text-zinc-100 font-bold tracking-wide mt-4", className)}>
            {children}
        </h4>
    );
};
export const CardDescription = ({
    className,
    children,
}: {
    className?: string;
    children: React.ReactNode;
}) => {
    return (
        <p
            className={cn(
                "mt-8 text-zinc-400 tracking-wide leading-relaxed text-sm",
                className
            )}
        >
            {children}
        </p>
    );
};

