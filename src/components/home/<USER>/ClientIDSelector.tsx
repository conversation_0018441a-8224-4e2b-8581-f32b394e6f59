import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from 'framer-motion';

interface ClientIDSelectorProps {
    clientData: any[];
    setSelectedClientId: (id: string) => void;
    currentClientId: string | null;
    disableDropdown?: boolean;  // New prop to control dropdown behavior
}

const ClientIDSelector = ({ clientData, setSelectedClientId, currentClientId, disableDropdown = false }: ClientIDSelectorProps) => {
    const [openDropDown, setOpenDropDown] = useState(false);
    const [selectedClient, setSelectedClient] = useState<{ sr_no: string; name: string } | null>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const modifiedClientData = clientData;


    // Toggle dropdown, but respect disableDropdown prop
    const toggleDropDown = () => {
        if (!disableDropdown) {
            setOpenDropDown(prevState => !prevState);
        }
    };

    // Handle client selection from the dropdown
    const handleClientSelect = (client: any, index: number) => {
        if (client.created_by == "All") {
            setSelectedClient({ sr_no: "All", name: client.name });
        } else {
            setSelectedClient({ sr_no: (index + 1).toString(), name: client.name });
        }

        setSelectedClientId(client.client_id);
        setOpenDropDown(false);
    };

    // Close dropdown on outside click
    const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
            setOpenDropDown(false);
        }
    };

    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Set the selected client when currentClientId is provided, or default to "All"
    useEffect(() => {
        if (modifiedClientData?.length > 0) {
            if (currentClientId) {
                const index = modifiedClientData.findIndex(client => client.client_id == currentClientId);
                if (index !== -1) {
                    const selected = modifiedClientData[index];
                    setSelectedClient({ sr_no: (index + 1).toString(), name: selected.name }); // Ensure sr_no starts from 1
                }
            } else {
                // Default to "All" if no currentClientId
                setSelectedClient({ sr_no: "All", name: "Display all clients" });
            }
        }
    }, [currentClientId, clientData, modifiedClientData]);

    return (
        <div className='w-[350px] lg:max-w-[380px] text-sm h-fit border-2 border-gray-300 rounded-lg bg-white relative' ref={dropdownRef}>
            {/* Dropdown toggle button */}
            <div className='flex text-gray-700 justify-between items-center p-2 cursor-pointer' onClick={toggleDropDown}>
                <div className='flex justify-between lg:w-[300px] gap-4 font-bold text-[16px]'>
                    {/* <span className='flex gap-2'>
                        <span>Sr No:</span> {selectedClient ? selectedClient.sr_no : ""}
                    </span> */}
                    <span className='flex gap-2'>
                        <span>Client Name:</span> <span className='w-[180px] truncate'>{selectedClient ? selectedClient.name : ( "All")}</span>
                    </span>
                </div>
                <div className='px-1'>
                    {openDropDown ? (
                        <ChevronUpIcon className='h-4 w-4 font-semibold' />
                    ) : (
                        <ChevronDownIcon className='h-4 w-4 font-semibold' />
                    )}
                </div>
            </div>

            {/* Dropdown menu */}
            <AnimatePresence>
                {openDropDown && !disableDropdown && (
                    <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className='w-[324px] mt-1 max-h-60 overflow-x-auto overflow-y-visible bg-white border-gray-300 border-2 z-50 absolute rounded-lg'
                    >
                        <table className="w-full table-auto">
                            <thead className='bg-secondary text-white'>
                                <tr>
                                    {/* <th className="border-b px-2 py-2 text-left w-[50px]">S.No</th> */}
                                    <th className="border-b px-2 py-2 text-left w-[200px]">Client Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                {modifiedClientData.map((client, index) => (
                                    <tr
                                        key={client.client_id}
                                        className="cursor-pointer hover:bg-gray-200"
                                        onClick={() => handleClientSelect(client, index)}
                                    >
                                        {/* <td className="border-b px-2 py-2">{index + 1}</td>  */}
                                        <td className="border-b px-2 py-2">{client.name}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default ClientIDSelector;
