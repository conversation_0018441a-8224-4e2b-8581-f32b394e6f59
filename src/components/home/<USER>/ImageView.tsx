import React, { useState } from 'react';
import Image from 'next/image';
import { XMarkIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface ImageViewProps { 
    clientInfo: any; 
    setshowImage: React.Dispatch<React.SetStateAction<boolean>>;
}

const ImageView: React.FC<ImageViewProps> = ({ clientInfo, setshowImage }) => {
    const fallbackLogo = '/logo_here.png';
    const [clientLogo, setClientLogo] = useState<string>(fallbackLogo);

    return (
        <div className='absolute top-0 left-0 right-0 bottom-0 bg-slate-800 bg-opacity-50 flex justify-center items-center z-50'>
            <motion.div
                drag 
                whileDrag={{ scale: 0.9 }}
                dragSnapToOrigin
                className="box"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
            >
                <div className='bg-white w-[300px] sm:w-[580px] md:w-[670px] lg:w-[1000px] h-[400px] lg:h-[550px] rounded-lg flex flex-col'>
                    <div className='flex justify-end w-full p-4'>
                        <button
                            className='w-fit h-fit'
                            onClick={() => setshowImage(false)} >
                            <XMarkIcon className='text-slate-600 w-8 h-8' />
                        </button>
                    </div>
                    <div className='relative w-full h-full flex justify-center items-center rounded-lg'>
                        <Image
                            className='object-contain p-2 border-secondary rounded-md'
                            src={clientInfo}
                            alt="Client Logo"
                            layout="fill" // Makes the image fill the container while maintaining aspect ratio
                            objectFit="contain" // Ensures the image maintains its aspect ratio
                        />
                    </div>
                </div>
            </motion.div>
        </div>
    );
};

export default ImageView;
