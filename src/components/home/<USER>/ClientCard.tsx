"use client"
import React, { useEffect, useState } from 'react'
import Image from "next/image";
import { useGetLogo } from '@/hooks/home/<USER>/useGetLogo';
import { useEditClient } from '@/hooks/home/<USER>/useEditClient';
import { useQueryClient } from '@tanstack/react-query'
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import { useNavigate } from 'react-router-dom';
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { useRemoveClient } from '@/hooks/home/<USER>/useRemoveClient';
import AskModal from '@/components/ui/extra/AskModal';
import { CardBody, CardContainer, CardItem } from "@/components/ui/3d-card";
import Link from "next/link";
import { Label } from '@/components/ui/label';
import { AnimatePresence, motion } from "framer-motion";
import { Input } from '@/components/ui/input';
import { CanvasRevealEffect } from '@/components/ui/canvas-reveal-effect';

interface ClientCardProps {
  clientListInfo: any;
  setImageData: React.Dispatch<React.SetStateAction<any>>;
  setshowImage: React.Dispatch<React.SetStateAction<boolean>>;
}

const ClientCard: React.FC<ClientCardProps> = ({ clientListInfo, setImageData, setshowImage }) => {

  const [editable, setEditable] = useState(false)
  const [clientId, setclientId] = useState(clientListInfo?.client_id)
  const [clientLogo, setClientLogo] = useState<any>('/empty.png');
  const [clientName, setClientName] = useState(clientListInfo?.name);
  const [clientDescription, setClientDescription] = useState(clientListInfo?.description);
  const [clientCreatedBy, setclientCreatedBy] = useState(clientListInfo?.created_by)
  const [file, setFile] = useState<File>(null);
  const [imagePreview, setImagePreview] = useState<string | null>()
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [modalType, setModalType] = useState<string>(''); // For the type of modal (msg, err)
  const [modalMessage, setModalMessage] = useState<string>(''); // For the message of the modal
  const [displayResponse, setDisplayResponse] = useState(false)

  const { data: clientLogoData } = useGetLogo(clientListInfo?.logo);

  useEffect(() => {
    setClientLogo(clientLogoData || '/empty.png')
  }, [clientLogoData])



  const handleProjectListClick = () => {
    // Passing data as state to the next page
    navigate('/home/<USER>/projectlist', {
      state: { myProp: clientId }
    });
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files ? e.target.files[0] : null;

    if (selectedFile) {
      // Check if the selected file's type is one of the allowed formats
      const validFormats = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];

      if (!validFormats.includes(selectedFile.type)) {
        setDisplayResponse(true)
        setModalType('err')
        setModalMessage('Invalid file format. Please upload an image ')
        return; // Exit the function if the file is not valid
      }

      setFile(selectedFile); // Store the actual File object in the state
      const objectUrl = URL.createObjectURL(selectedFile);
      setImagePreview(objectUrl); // Store the object URL in the state
    } else {
      console.error('No file selected or invalid file');
    }
  };



  // Call the editClient mutation with appropriate parameters
  const editClient = useEditClient(clientId, clientName, clientDescription, file);
  const deleteClient = useRemoveClient(clientId)

  const urlToFile = async (url, filename, mimeType) => {
    const response = await fetch(url);
    const data = await response.blob();
    return new File([data], filename, { type: mimeType });
  };

  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      const formData = new FormData();
      formData.append('name', clientName);
      formData.append('description', clientDescription);

      let finalFile = file;
      if (!file) {
        const currentImageFile = await urlToFile(clientLogo, 'current_logo.png', 'image/png');
        finalFile = currentImageFile;
      }

      if (finalFile) {
        formData.append('file', finalFile); // Append the final file
      }


      const response = await editClient.mutateAsync(formData);
      setFile(null)

      queryClient.invalidateQueries({ queryKey: ['GetClientInfo'] });
      queryClient.invalidateQueries({ queryKey: ['useGetLogo'] });
      setFile(null);

      if (response.msg) {

      }
      else if (response.err) {
        setDisplayResponse(true)
      }
    } catch (error) {
      console.error('Error uploading client info:', error);
      // alert('Error saving client info.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClient = async () => {
    const response = await deleteClient.mutateAsync()

    if (response.msg) {

    }
    else if (response.err) {
      setDisplayResponse(true)
    }

  }

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const sanitizedLogo = clientLogo && (clientLogo.startsWith('http://') || clientLogo.startsWith('https://') || clientLogo.startsWith('/'))
    ? clientLogo
    : '/empty.png'; // Fallback to a default image if the URL is invalid or not provided

  const [hovered, setHovered] = useState(false)


  return (
    <div
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {showDeleteModal && (
        <AskModal
          questionToAsk="Do you want to continue?"
          questionDetail="Deleting the client cannot be undone."
          onYes={handleDeleteClient}
          onNo={closeDeleteModal}
          closeModal={closeDeleteModal}
        />
      )}
      <CardContainer className="inter-var shadow-lg bg-gradient-to-t from-gray-950 to-[84%]">
        {hovered && <AnimatePresence>
          <div className="h-full w-full absolute inset-0">
            <CanvasRevealEffect
              animationSpeed={3}
              containerClassName="bg-transparent"
              colors={[
                [236, 72, 153],
                [232, 121, 249],
              ]}
              dotSize={2}
            />
          </div>
        </AnimatePresence>}
        <CardBody className="relative group/card  dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-black dark:border-white/[0.2] border-slate-500 w-auto sm:w-[30rem] lg:w-[500px] rounded-xl p-4 border-[1px] h-fit hover:bg-transparent">
          <div className='flex flex-col lg:flex-row w-full justify-between lg:px-10'>
            <CardItem
              translateZ="50"
              className="text-[13px] text-slate-200 dark:text-slate-200"
            >
              {/* Creation Date:  */}
              {new Date(clientListInfo?.creation_date).toLocaleString()}
            </CardItem>
          </div>
          <div className='flex justify-between w-full flex-col lg:flex-row'>
            <CardItem translateZ="100" className="w-full mt-4">
              <Image
                src={imagePreview || clientLogo}
                height="1000"
                width="1000"
                className="h-[200px] w-full lg:w-[200px] object-cover rounded-xl group-hover/card:shadow-xl"
                alt="thumbnail"
              />
            </CardItem>
            <div className='w-full h-fit flex flex-col justify-between items-center'>
              <div className='w-full'>
                <CardItem translateZ="100" className="w-full mt-4">
                  <Label className='text-[12px] text-slate-200'>Client Name: </Label>
                  <Input
                    type="text"
                    placeholder="Client Name"
                    value={clientName}
                    className='text-slate-200 bg-transparent text-[20px]'
                    onChange={(e) => setClientName(e.target.value)}
                    disabled={!editable} // Disable when editable is false
                  />
                </CardItem>
                <CardItem translateZ="100" className="w-full mt-4">
                  <Label className='text-[12px] text-slate-200'>Client Description: </Label>
                  <Input
                    placeholder="Client Description"
                    value={clientDescription}
                    className='text-slate-200 bg-transparent text-[20px]'
                    onChange={(e) => setClientDescription(e.target.value)}
                    disabled={!editable} // Disable when editable is false
                  />
                </CardItem>
              </div>
              <div className='flex gap-10 w-full justify-around text-xs mt-3'>
                <CardItem
                  translateZ="50"
                  className="text-slate-200 dark:text-slate-200"
                >
                  {/* Cliend ID:  */}
                  {clientId || ""}
                </CardItem>
                <CardItem
                  translateZ="50"
                  className="text-slate-200 dark:text-slate-200"
                >
                  {/* Created By:  */}
                  {clientCreatedBy}
                </CardItem>
              </div>
            </div>

          </div>

          <div className="flex justify-center items-center mt-10 gap-4 w-full">
            <CardItem
              translateZ={20}
              className="text-[13px] lg:text-sm text-slate-200 py-[7.5px] px-2 rounded-md"
            >
              <button
                className="p-[3px] relative"
                onClick={() => {
                  if (editable) {
                    document.getElementById('fileInput').click(); // Trigger file input
                  } else {
                    window.location.href = `/home/<USER>/${clientId}?clientId=${clientId}`;
                  }
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full" />
                <div className="px-8 py-2  bg-black rounded-full relative group transition duration-200 text-white hover:bg-transparent">
                  {editable ? "Choose Photo" : "Go to Projects"}
                </div>
              </button>
              <input
                id="fileInput"
                type="file"
                style={{ display: 'none' }} // Hide the file input
                onChange={handleFileChange} // Handle file selection
              />
            </CardItem>
            <CardItem
              translateZ={20}
              className={`text-[13px] lg:text-sm text-slate-200 py-[7.5px] px-2 rounded-md`}
              onClick={() => {
                setEditable(!editable);
                if (editable) {

                  handleSubmit();

                }
              }}
            >
              <button className="p-[3px] relative">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full" />
                <div className="px-8 py-2  bg-black rounded-full relative group transition duration-200 text-white hover:bg-transparent">
                  {!editable ? "Edit Client" : "Save"}
                </div>
              </button>

            </CardItem>
            <CardItem
              translateZ={20}
              as="button"
              className={`text-[13px] lg:text-sm text-slate-200 bg-red-800 py-[7.5px] px-4 rounded-full hover:bg-red-600 duration-75`}
              onClick={() => {
                // handleDeleteClientPopUp()
                setShowDeleteModal(true)
              }}
            >
              Delete
            </CardItem>



          </div>
        </CardBody>
      </CardContainer>
    </div>
  )
}

export default ClientCard