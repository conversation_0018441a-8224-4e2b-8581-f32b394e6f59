"use client"
import React, { useEffect, useState } from 'react'
import Image from "next/image";
import { useGetLogo } from '@/hooks/home/<USER>/useGetLogo';
import { useEditClient } from '@/hooks/home/<USER>/useEditClient';
import { useQueryClient } from '@tanstack/react-query'
import { BuildingOffice2Icon, UserIcon, CalendarDaysIcon } from "@heroicons/react/24/outline";
import { useNavigate } from 'react-router-dom';
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { useRemoveClient } from '@/hooks/home/<USER>/useRemoveClient';
import AskModal from '@/components/ui/extra/AskModal';
import Link from "next/link";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

interface ClientCardProps {
  clientListInfo: any;
  setImageData: React.Dispatch<React.SetStateAction<any>>;
  setshowImage: React.Dispatch<React.SetStateAction<boolean>>;
}

const SimplifiedClientCard: React.FC<ClientCardProps> = ({ clientListInfo, setImageData, setshowImage }) => {
  const [editable, setEditable] = useState(false)
  const [clientId, setclientId] = useState(clientListInfo?.client_id)
  const [clientLogo, setClientLogo] = useState<any>('/empty.png');
  const [clientName, setClientName] = useState(clientListInfo?.name);
  const [clientDescription, setClientDescription] = useState(clientListInfo?.description);
  const [clientCreatedBy, setclientCreatedBy] = useState(clientListInfo?.created_by)
  const [file, setFile] = useState<File>(null);
  const [imagePreview, setImagePreview] = useState<string | null>()
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [modalType, setModalType] = useState<string>(''); // For the type of modal (msg, err)
  const [modalMessage, setModalMessage] = useState<string>(''); // For the message of the modal
  const [displayResponse, setDisplayResponse] = useState(false)

  const { data: clientLogoData } = useGetLogo(clientListInfo?.logo);
  const queryClient = useQueryClient();

  useEffect(() => {
    setClientLogo(clientLogoData || '/empty.png')
  }, [clientLogoData])

  const handleProjectListClick = () => {
    // Passing data as state to the next page
    window.location.href = `/home/<USER>/${clientId}?clientId=${clientId}`;
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files ? e.target.files[0] : null;

    if (selectedFile) {
      // Check if the selected file's type is one of the allowed formats
      const validFormats = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];

      if (!validFormats.includes(selectedFile.type)) {
        setDisplayResponse(true)
        setModalType('err')
        setModalMessage('Invalid file format. Please upload an image ')
        return; // Exit the function if the file is not valid
      }

      setFile(selectedFile); // Store the actual File object in the state
      const objectUrl = URL.createObjectURL(selectedFile);
      setImagePreview(objectUrl); // Store the object URL in the state
    } else {
      console.error('No file selected or invalid file');
    }
  };

  // Call the editClient mutation with appropriate parameters
  const editClient = useEditClient(clientId, clientName, clientDescription, file);
  const deleteClient = useRemoveClient(clientId)

  const urlToFile = async (url, filename, mimeType) => {
    const response = await fetch(url);
    const data = await response.blob();
    return new File([data], filename, { type: mimeType });
  };

  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      const formData = new FormData();
      formData.append('name', clientName);
      formData.append('description', clientDescription);

      let finalFile = file;
      if (!file) {
        const currentImageFile = await urlToFile(clientLogo, 'current_logo.png', 'image/png');
        finalFile = currentImageFile;
      }

      if (finalFile) {
        formData.append('file', finalFile); // Append the final file
      }

      const response = await editClient.mutateAsync(formData);
      setFile(null)

      queryClient.invalidateQueries({ queryKey: ['GetClientInfo'] });
      queryClient.invalidateQueries({ queryKey: ['useGetLogo'] });
      setFile(null);

      if (response.msg) {
        // Success
      }
      else if (response.err) {
        setDisplayResponse(true)
      }
    } catch (error) {
      console.error('Error uploading client info:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClient = async () => {
    const response = await deleteClient.mutateAsync()

    if (response.msg) {
      // Success
    }
    else if (response.err) {
      setDisplayResponse(true)
    }
  }

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const sanitizedLogo = clientLogo && (clientLogo.startsWith('http://') || clientLogo.startsWith('https://') || clientLogo.startsWith('/'))
    ? clientLogo
    : '/empty.png'; // Fallback to a default image if the URL is invalid or not provided

  return (
    <div>
      {showDeleteModal && (
        <AskModal
          questionToAsk="Do you want to continue?"
          questionDetail="Deleting the client cannot be undone."
          onYes={handleDeleteClient}
          onNo={closeDeleteModal}
          closeModal={closeDeleteModal}
        />
      )}

      <Card className="overflow-hidden h-[450px] flex flex-col" shadow="sm">
        {/* Static Header with primary background */}
        <CardHeader className="bg-primary-50 border-b border-primary-100 flex-shrink-0">
          <div className="flex items-center justify-center mb-3">
            {imagePreview || clientLogo !== '/empty.png' ? (
              <Image
                src={imagePreview || clientLogo}
                height="100"
                width="100"
                className="h-16 w-16 object-cover rounded-lg border-2 border-primary-200"
                alt="Client logo"
              />
            ) : (
              <BuildingOffice2Icon className="h-16 w-16 text-primary-600" />
            )}
          </div>
          {!editable && (
            <>
              <CardTitle className="text-lg mb-1 text-center">{clientName}</CardTitle>
              <p className="text-xs text-gray-600 font-medium text-center">Client ID: {clientId}</p>
            </>
          )}
        </CardHeader>

        {/* Compact Content */}
        <CardContent className="py-3 flex-grow flex flex-col justify-between">
          {editable ? (
            <div className="space-y-3">
              <div>
                <Label className="text-xs font-medium text-gray-500 mb-1">Client Name:</Label>
                <Input
                  type="text"
                  placeholder="Client Name"
                  value={clientName}
                  className="text-gray-800 bg-white text-sm h-8"
                  onChange={(e) => setClientName(e.target.value)}
                />
              </div>

              <div>
                <Label className="text-xs font-medium text-gray-500 mb-1">Client Description:</Label>
                <Input
                  placeholder="Client Description"
                  value={clientDescription}
                  className="text-gray-800 bg-white text-sm h-8"
                  onChange={(e) => setClientDescription(e.target.value)}
                />
              </div>
            </div>
          ) : (
            <>
              <div className="mb-3">
                <p className="text-sm text-gray-700 leading-tight line-clamp-3">{clientDescription}</p>
              </div>

              <div className="grid grid-cols-2 gap-3 text-xs">
                <div>
                  <span className="text-gray-500 font-medium">Created By:</span>
                  <div className="flex items-center mt-1">
                    <UserIcon className="h-3 w-3 mr-1 text-gray-400" />
                    <span className="text-gray-700 font-medium truncate">{clientCreatedBy}</span>
                  </div>
                </div>
                <div>
                  <span className="text-gray-500 font-medium">Created:</span>
                  <div className="flex items-center mt-1">
                    <CalendarDaysIcon className="h-3 w-3 mr-1 text-gray-400" />
                    <span className="text-gray-700 font-medium">{new Date(clientListInfo?.creation_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: '2-digit' })}</span>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>

        {/* Actions - Fixed at bottom */}
        <div className="flex-shrink-0 px-4 pb-4 pt-2 border-t border-gray-100">
          <div className="space-y-1.5">
            <Button
              variant={editable ? "secondary" : "primary"}
              size="sm"
              fullWidth
              onClick={() => {
                if (editable) {
                  document.getElementById('fileInput').click(); // Trigger file input
                } else {
                  handleProjectListClick();
                }
              }}
              className="text-xs"
            >
              {editable ? "Choose Photo" : "View Projects"}
            </Button>

            <Button
              variant={editable ? "success" : "secondary"}
              size="sm"
              fullWidth
              onClick={() => {
                setEditable(!editable);
                if (editable) {
                  handleSubmit();
                }
              }}
              className="text-xs"
              isLoading={isLoading}
            >
              {!editable ? "Edit Client" : "Save Changes"}
            </Button>

            <Button
              variant="error"
              size="sm"
              fullWidth
              onClick={() => setShowDeleteModal(true)}
              className="text-xs"
            >
              Delete Client
            </Button>

            <input
              id="fileInput"
              type="file"
              style={{ display: 'none' }} // Hide the file input
              onChange={handleFileChange} // Handle file selection
            />
          </div>
        </div>
      </Card>
    </div>
  )
}

export default SimplifiedClientCard;
