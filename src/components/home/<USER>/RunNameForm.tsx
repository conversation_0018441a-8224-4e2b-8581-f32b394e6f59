"use client";
import React, { useState } from "react";
import { usePostRunName } from "@/hooks/home/<USER>/usePostRunName";
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { ArrowRightIcon, SparklesIcon, InformationCircleIcon } from "@heroicons/react/24/outline";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const RunNameForm = ({ handleSave }:any) => {
  const [runName, setRunName] = useState("");
  const [error, setError] = useState("");
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [isRunDetailSubmitted, setIsRunDetailSubmitted] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async () => {
    if (!runName.trim()) {
      setError("Run name is required");
      return;
    }

    setIsProcessing(true);
    setError("");

    try {
      await handleSave();
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      <div className="flex justify-center items-center h-full w-full p-4">
        <div className="w-full max-w-xl">
          {/* Header Section */}
          <div className="text-center mb-4">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full mb-2">
              <SparklesIcon className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              Create Assessment Run
            </h2>
            <p className="text-sm text-gray-600 max-w-md mx-auto">
              Start your talent assessment journey by creating a new run
            </p>
          </div>

          {/* Main Form Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-50 to-transparent rounded-full -translate-y-12 translate-x-12"></div>

            <div className="relative">
              <div className="flex items-center gap-2 mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Run Configuration</h3>
                <button
                  onClick={() => {/* Add tooltip or help modal */}}
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                  aria-label="Information"
                >
                  <InformationCircleIcon className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="runName" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                    Assessment Run Name
                    <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="runName"
                      type="text"
                      placeholder="e.g., Senior Developer Assessment Q1 2024"
                      className={`w-full h-10 text-sm transition-all duration-200 ${
                        error
                          ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                          : "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      }`}
                      value={runName}
                      onChange={(e) => {
                        setRunName(e.target.value);
                        if (error) setError("");
                      }}
                    />
                  </div>
                  {error && (
                    <div className="flex items-center gap-1 text-xs text-red-600">
                      <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                      {error}
                    </div>
                  )}
                  <p className="text-xs text-gray-500">
                    Choose a descriptive name to identify this assessment run
                  </p>
                </div>

                {/* Action Button */}
                <div className="pt-2">
                  <button
                    onClick={handleSubmit}
                    disabled={isProcessing || !runName.trim()}
                    className={`w-full py-3 px-4 text-sm font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-300
                      ${isProcessing || !runName.trim()
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transform hover:scale-[1.01]'
                      }
                      text-white flex items-center justify-center gap-2 relative overflow-hidden group`}
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                        <span>Creating Run...</span>
                      </>
                    ) : (
                      <>
                        <SparklesIcon className="h-4 w-4 group-hover:animate-pulse" />
                        <span>Create Assessment Run</span>
                        <ArrowRightIcon className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </>
                    )}
                  </button>
                  <p className="text-xs text-center text-gray-500 mt-2">
                    This will initialize your assessment workflow
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Help Section */}
          <div className="mt-4 bg-blue-50 rounded-lg p-3 border border-blue-100">
            <div className="flex items-start gap-2">
              <InformationCircleIcon className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-xs font-medium text-blue-900 mb-1">What happens next?</h4>
                <p className="text-xs text-blue-700">
                  Upload job descriptions, candidate CVs, configure assessment parameters, and generate reports.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isRunDetailSubmitted && modalType && (
        <ResponseModal
          type={modalType}
          message={modalMessage}
          onClose={() => {
            setModalType(null);
            setModalMessage("");
            setIsRunDetailSubmitted(false);
          }}
        />
      )}
    </>
  );
};

export default RunNameForm;
