import React, { useState } from "react";
import Link from "next/link";
import { BackgroundGradient } from "@/components/ui/background-gradient";
import * as motion from "motion/react-client";
import {
  ArrowPathRoundedSquareIcon,
  InboxArrowDownIcon,
  PaperAirplaneIcon,
  TrashIcon,
  PlusCircleIcon,
} from "@heroicons/react/24/outline";
import { getDownloadReport } from '@/api/home/<USER>/download/getDownloadReport';
import { useRemoveRun } from "@/hooks/home/<USER>/useRemoveRun";
import AskModal from "@/components/ui/extra/AskModal";
import { CanvasRevealEffect } from "@/components/ui/canvas-reveal-effect";
import { AnimatePresence } from "framer-motion";

const RunListCard = ({ sortedrunlistData, handleDownloadClick, ProjectIdvalue }) => {
  const isThresholdPresent =
    sortedrunlistData.threshold !== "--" &&
    sortedrunlistData.threshold != null &&
    sortedrunlistData.threshold !== 0;

  const deleteRun = useRemoveRun(sortedrunlistData.run_id);

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleRunDelete = async () => {
    const response = await deleteRun.mutateAsync();

    if (response.msg) {
      // Handle success
    } else if (response.err) {
      // Handle error
    }
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const buttonConfigs = [
    {
      condition: true ,
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 2); },
      label: "Download Profile Report",
      icon: <InboxArrowDownIcon className="text-slate-200 w-6 h-6" />,
    },
    {
      condition: true,
      onClick: () => {
        window.location.href = `/home/<USER>
      },
      label: "View Profile Report",
      icon: <PaperAirplaneIcon className="text-slate-200 w-6 h-6" />,
    },
    {
      condition: true,
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 3); },
      label: "Download Shortlist Report",
      icon: <InboxArrowDownIcon className="text-slate-200 w-6 h-6" />,
    },
    {
      condition: true,
      onClick: () => {
        window.location.href = `/home/<USER>
      },
      label: "View Shortlist Report",
      icon: <PaperAirplaneIcon className="text-slate-200 w-6 h-6" />,
    },
    {
      condition: true,
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 4); },
      label: "Download Final Report",
      icon: <InboxArrowDownIcon className="text-slate-200 w-6 h-6" />,
    },
    {
      condition: true,
      onClick: () => { setShowDeleteModal(true) },
      label: "Delete Run",
      icon: <TrashIcon className="text-white w-6 h-6" />,
    }
  ];

  const filteredButtons = buttonConfigs.filter((btn) => btn.condition);
  const [hovered, setHovered] = useState(false);

  return (
    <BackgroundGradient className="rounded-[22px] w-full md:w-[450px] lg:w-[600px] p-4 bg-gradient-to-t to-[84%] dark:bg-zinc-900">
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          duration: 0.4,
          scale: { type: "spring", visualDuration: 0.4, bounce: 0.5 },
        }}
        key={sortedrunlistData.run_id}
        className="flex flex-col gap-4 p-6 w-full md:w-[450px] lg:w-[600px] min-h-[500px] rounded-md bg-transparent"
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        {showDeleteModal && (
          <AskModal
            questionToAsk="Do you want to continue?"
            questionDetail="Deleting the client cannot be undone."
            onYes={handleRunDelete}
            onNo={closeDeleteModal}
            closeModal={closeDeleteModal}
          />
        )}
        {hovered && (
          <AnimatePresence>
            <div className="h-full w-full absolute inset-0">
              <CanvasRevealEffect
                animationSpeed={3}
                containerClassName="bg-transparent"
                colors={[
                  [236, 72, 153],
                  [232, 121, 249],
                ]}
                dotSize={1}
              />
            </div>
          </AnimatePresence>
        )}
        <div className="flex flex-col gap-10 w-full text-slate-200 z-50">

          <div className="flex flex-col lg:flex-row justify-evenly px-8">


            <div className="flex flex-col gap-4 w-full">
              <div className="text-xl dark:text-white">
                <h1 className="text-[12px]">File Name Of JD:</h1>
                <div>{sortedrunlistData.run_name}</div>
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>File Name Of JD:</h1>
                {sortedrunlistData.job_descriptions.map((jd) => (
                  <div className="text-[20px] h-[40px]" key={jd.jd_id}>{jd.jd_name}</div>
                ))}
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>File Name Of CV:</h1>
                {sortedrunlistData.curriculum_vitae.map((cv) => (
                  <div className="text-[20px] h-[40px]" key={cv.cv_id}>{cv.cv_name}</div>
                ))}
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>Result Generated:</h1>
                <div className="text-[20px] h-[40px]">{sortedrunlistData.report_generated ? "True" : "False"}</div>
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>No Of CV:</h1>
                <div className="text-[20x]">{sortedrunlistData.no_of_cv}</div>
              </div>
            </div>


            <div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>Additional Information:</h1>
                <div className="text-[20px] h-[40px]">{sortedrunlistData.additional_information}</div>
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>Threshold:</h1>{" "}
                <div className="text-[20px] h-[40px]">
                  {sortedrunlistData.no_of_cv === 1
                    ? "N/A"
                    : sortedrunlistData.threshold !== null && sortedrunlistData.threshold !== undefined
                      ? sortedrunlistData.threshold
                      : "N/A"}
                </div>
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>Similarity Percentage:</h1>
                <div className="text-[20px] h-[40px]">{sortedrunlistData.cv_similarity_percentage}%</div>
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>Consistency Percentage:</h1>
                <div className="text-[20px] h-[40px]">{sortedrunlistData.cv_consistency_percentage}%</div>
              </div>
              <div className="text-[12px] dark:text-gray-300">
                <h1>Creation Date:</h1>{" "}
                <div className="text-[20px] h-[40px]">
                  {new Date(sortedrunlistData.creation_date).toLocaleString()}
                </div>
              </div>
            </div>
          </div>



          <div className={`flex flex-col ${!sortedrunlistData.report_generated ? "gap-10" : "gap-4"} w-full justify-between h-fit lg:px-20`}>
            {!sortedrunlistData.report_generated && (
              <div className="text-center text-2xl">
                Run not completed!
              </div>
            )}
            {filteredButtons.map((btn, index) => (
              <div key={index} className="flex w-full">
                <button className={`bg-slate-800 no-underline group cursor-pointer relative shadow-2xl shadow-zinc-900 rounded-full p-px text-xs font-semibold leading-6 w-full text-white inline-block
                
                ${index === filteredButtons.length - 1
                    ? "bg-red-800 hover:bg-red-600 text-white"
                    : "hover:bg-secondary hover:text-white"
                  }`}
                  onClick={btn.onClick}
                >
                  <span className="absolute inset-0 overflow-hidden rounded-full">
                    <span className="absolute inset-0 rounded-full bg-[image:radial-gradient(75%_100%_at_50%_0%,rgba(56,189,248,0.6)_0%,rgba(56,189,248,0)_75%)] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
                  </span>
                  <div className="relative flex space-x-2 items-center z-10 rounded-full bg-zinc-950 py-0.5 px-4 ring-1 ring-white/10 ">
                    <span className="w-full flex p-3 justify-start gap-2">
                      <div className="flex justify-center items-center">{btn.icon}</div>
                      <p className="flex justify-center items-center">{btn.label}</p></span>
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="1.5"
                        d="M10.75 8.75L14.25 12L10.75 15.25"
                      ></path>
                    </svg>
                  </div>
                  <span className="absolute -bottom-0 left-[1.125rem] h-px w-[calc(100%-2.25rem)] bg-gradient-to-r from-emerald-400/0 via-emerald-400/90 to-emerald-400/0 transition-opacity duration-500 group-hover:opacity-40"></span>
                </button>
              </div>
            ))}
          </div>
        </div>
      </motion.div>
    </BackgroundGradient>
  );
};

export default RunListCard;