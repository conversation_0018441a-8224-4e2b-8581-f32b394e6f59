import React, { useState } from "react";
import {
  DocumentTextIcon,
  UserIcon,
  CalendarDaysIcon,
  InboxArrowDownIcon,
  EyeIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import { getDownloadReport } from '@/api/home/<USER>/download/getDownloadReport';
import { useRemoveRun } from "@/hooks/home/<USER>/useRemoveRun";
import AskModal from "@/components/ui/extra/AskModal";

const SimplifiedRunListCard = ({ sortedrunlistData, handleDownloadClick, ProjectIdvalue }) => {
  const isThresholdPresent =
    sortedrunlistData.threshold !== "--" &&
    sortedrunlistData.threshold != null &&
    sortedrunlistData.threshold !== 0;

  const deleteRun = useRemoveRun(sortedrunlistData.run_id);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleRunDelete = async () => {
    const response = await deleteRun.mutateAsync();

    if (response.msg) {
      // Handle success
    } else if (response.err) {
      // Handle error
    }
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const reportActions = [
    {
      label: "Download Profile Report",
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 2); },
      icon: InboxArrowDownIcon,
      type: "download"
    },
    {
      label: "View Profile Report",
      onClick: () => {
        window.location.href = `/home/<USER>
      },
      icon: EyeIcon,
      type: "view"
    },
    {
      label: "Download Shortlist Report",
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 3); },
      icon: InboxArrowDownIcon,
      type: "download"
    },
    {
      label: "View Shortlist Report",
      onClick: () => {
        window.location.href = `/home/<USER>
      },
      icon: EyeIcon,
      type: "view"
    },
    {
      label: "Download Final Report",
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 4); },
      icon: InboxArrowDownIcon,
      type: "download"
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-[600px] flex flex-col">
      {showDeleteModal && (
        <AskModal
          questionToAsk="Do you want to continue?"
          questionDetail="Deleting the run cannot be undone."
          onYes={handleRunDelete}
          onNo={closeDeleteModal}
          closeModal={closeDeleteModal}
        />
      )}

      {/* Static Header with light blue background */}
      <div className="bg-blue-50 p-4 border-b border-blue-100 flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">{sortedrunlistData.run_name}</h3>
        <div className="flex items-center justify-between">
          <p className="text-xs text-gray-600 font-medium">Run ID: {sortedrunlistData.run_id}</p>
          {sortedrunlistData.report_generated ? (
            <span className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">
              Completed
            </span>
          ) : (
            <span className="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full font-medium">
              Pending
            </span>
          )}
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="p-4 flex-grow overflow-y-auto flex flex-col">

        {/* Run Details in Two Columns */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          {/* Left Column */}
          <div className="space-y-3">
            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">File Name Of JD:</h4>
              {sortedrunlistData.job_descriptions.map((jd) => (
                <p key={jd.jd_id} className="text-sm text-gray-800 font-medium">{jd.jd_name}</p>
              ))}
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">File Name Of CV:</h4>
              <div className="space-y-0.5">
                {sortedrunlistData.curriculum_vitae.map((cv) => (
                  <p key={cv.cv_id} className="text-sm text-gray-800 font-medium">{cv.cv_name}</p>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Result Generated:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {sortedrunlistData.report_generated ? "True" : "False"}
              </p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">No Of CV:</h4>
              <p className="text-sm text-gray-800 font-medium">{sortedrunlistData.no_of_cv}</p>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-3">
            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Additional Information:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {sortedrunlistData.additional_information || "N/A"}
              </p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Threshold:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {sortedrunlistData.no_of_cv === 1
                  ? "N/A"
                  : sortedrunlistData.threshold !== null && sortedrunlistData.threshold !== undefined
                    ? sortedrunlistData.threshold
                    : "N/A"}
              </p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Similarity Percentage:</h4>
              <p className="text-sm text-gray-800 font-medium">{sortedrunlistData.cv_similarity_percentage}%</p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Consistency Percentage:</h4>
              <p className="text-sm text-gray-800 font-medium">{sortedrunlistData.cv_consistency_percentage}%</p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Creation Date:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {new Date(sortedrunlistData.creation_date).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Actions - Fixed at bottom outside scroll area */}
      <div className="flex-shrink-0 p-4 pt-0 border-t border-gray-100">
          {!sortedrunlistData.report_generated ? (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500 font-medium">Run not completed!</p>
            </div>
          ) : (
            <div className="space-y-2">
            {/* Download Profile Report */}
            <button
              onClick={() => { getDownloadReport(sortedrunlistData.run_id, 2); }}
              className="w-full py-2.5 px-4 rounded-md font-medium text-sm flex items-center justify-start gap-3 bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors duration-200"
            >
              <InboxArrowDownIcon className="h-4 w-4" />
              Download Profile Report
            </button>

            {/* View Profile Report */}
            <button
              onClick={() => {
                window.location.href = `/home/<USER>
              }}
              className="w-full py-2.5 px-4 rounded-md font-medium text-sm flex items-center justify-start gap-3 bg-slate-100 text-slate-700 hover:bg-slate-200 transition-colors duration-200"
            >
              <EyeIcon className="h-4 w-4" />
              View Profile Report
            </button>

            {/* Download Shortlist Report */}
            <button
              onClick={() => { getDownloadReport(sortedrunlistData.run_id, 3); }}
              className="w-full py-2.5 px-4 rounded-md font-medium text-sm flex items-center justify-start gap-3 bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors duration-200"
            >
              <InboxArrowDownIcon className="h-4 w-4" />
              Download Shortlist Report
            </button>

            {/* View Shortlist Report */}
            <button
              onClick={() => {
                window.location.href = `/home/<USER>
              }}
              className="w-full py-2.5 px-4 rounded-md font-medium text-sm flex items-center justify-start gap-3 bg-slate-100 text-slate-700 hover:bg-slate-200 transition-colors duration-200"
            >
              <EyeIcon className="h-4 w-4" />
              View Shortlist Report
            </button>

            {/* Download Final Report */}
            <button
              onClick={() => { getDownloadReport(sortedrunlistData.run_id, 4); }}
              className="w-full py-2.5 px-4 rounded-md font-medium text-sm flex items-center justify-start gap-3 bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors duration-200"
            >
              <InboxArrowDownIcon className="h-4 w-4" />
              Download Final Report
            </button>

            {/* Delete Run */}
            <button
              onClick={() => setShowDeleteModal(true)}
              className="w-full py-2.5 px-4 rounded-md font-medium text-sm flex items-center justify-start gap-3 bg-red-100 text-red-700 hover:bg-red-200 transition-colors duration-200"
            >
              <TrashIcon className="h-4 w-4" />
              Delete Run
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimplifiedRunListCard;
