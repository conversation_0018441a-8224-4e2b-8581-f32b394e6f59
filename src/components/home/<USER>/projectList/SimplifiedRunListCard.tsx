import React, { useState } from "react";
import {
  DocumentTextIcon,
  UserIcon,
  CalendarDaysIcon,
  InboxArrowDownIcon,
  EyeIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import { getDownloadReport } from '@/api/home/<USER>/download/getDownloadReport';
import { useRemoveRun } from "@/hooks/home/<USER>/useRemoveRun";
import AskModal from "@/components/ui/extra/AskModal";
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

const SimplifiedRunListCard = ({ sortedrunlistData, handleDownloadClick, ProjectIdvalue }) => {
  const isThresholdPresent =
    sortedrunlistData.threshold !== "--" &&
    sortedrunlistData.threshold != null &&
    sortedrunlistData.threshold !== 0;

  const deleteRun = useRemoveRun(sortedrunlistData.run_id);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleRunDelete = async () => {
    const response = await deleteRun.mutateAsync();

    if (response.msg) {
      // Handle success
    } else if (response.err) {
      // Handle error
    }
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const reportActions = [
    {
      label: "Download Profile Report",
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 2); },
      icon: InboxArrowDownIcon,
      type: "download"
    },
    {
      label: "View Profile Report",
      onClick: () => {
        window.location.href = `/home/<USER>
      },
      icon: EyeIcon,
      type: "view"
    },
    {
      label: "Download Shortlist Report",
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 3); },
      icon: InboxArrowDownIcon,
      type: "download"
    },
    {
      label: "View Shortlist Report",
      onClick: () => {
        window.location.href = `/home/<USER>
      },
      icon: EyeIcon,
      type: "view"
    },
    {
      label: "Download Final Report",
      onClick: () => { getDownloadReport(sortedrunlistData.run_id, 4); },
      icon: InboxArrowDownIcon,
      type: "download"
    }
  ];

  return (
    <Card className="overflow-hidden h-[600px] flex flex-col" shadow="sm">
      {showDeleteModal && (
        <AskModal
          questionToAsk="Do you want to continue?"
          questionDetail="Deleting the run cannot be undone."
          onYes={handleRunDelete}
          onNo={closeDeleteModal}
          closeModal={closeDeleteModal}
        />
      )}

      {/* Static Header with primary background */}
      <CardHeader className="bg-primary-50 border-b border-primary-100 flex-shrink-0">
        <CardTitle className="text-lg mb-2">{sortedrunlistData.run_name}</CardTitle>
        <div className="flex items-center justify-between">
          <p className="text-xs text-gray-600 font-medium">Run ID: {sortedrunlistData.run_id}</p>
          {sortedrunlistData.report_generated ? (
            <span className="bg-success-100 text-success-800 text-xs px-3 py-1 rounded-full font-medium">
              Completed
            </span>
          ) : (
            <span className="bg-warning-100 text-warning-800 text-xs px-3 py-1 rounded-full font-medium">
              Pending
            </span>
          )}
        </div>
      </CardHeader>

      {/* Scrollable Content */}
      <CardContent className="flex-grow overflow-y-auto flex flex-col">

        {/* Run Details in Two Columns */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          {/* Left Column */}
          <div className="space-y-3">
            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">File Name Of JD:</h4>
              {sortedrunlistData.job_descriptions.map((jd) => (
                <p key={jd.jd_id} className="text-sm text-gray-800 font-medium">{jd.jd_name}</p>
              ))}
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">File Name Of CV:</h4>
              <div className="space-y-0.5">
                {sortedrunlistData.curriculum_vitae.map((cv) => (
                  <p key={cv.cv_id} className="text-sm text-gray-800 font-medium">{cv.cv_name}</p>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Result Generated:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {sortedrunlistData.report_generated ? "True" : "False"}
              </p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">No Of CV:</h4>
              <p className="text-sm text-gray-800 font-medium">{sortedrunlistData.no_of_cv}</p>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-3">
            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Additional Information:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {sortedrunlistData.additional_information || "N/A"}
              </p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Threshold:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {sortedrunlistData.no_of_cv === 1
                  ? "N/A"
                  : sortedrunlistData.threshold !== null && sortedrunlistData.threshold !== undefined
                    ? sortedrunlistData.threshold
                    : "N/A"}
              </p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Similarity Percentage:</h4>
              <p className="text-sm text-gray-800 font-medium">{sortedrunlistData.cv_similarity_percentage}%</p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Consistency Percentage:</h4>
              <p className="text-sm text-gray-800 font-medium">{sortedrunlistData.cv_consistency_percentage}%</p>
            </div>

            <div>
              <h4 className="text-xs font-medium text-gray-500 mb-1">Creation Date:</h4>
              <p className="text-sm text-gray-800 font-medium">
                {new Date(sortedrunlistData.creation_date).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Actions - Fixed at bottom outside scroll area */}
      <div className="flex-shrink-0 p-4 pt-0 border-t border-gray-100">
          {!sortedrunlistData.report_generated ? (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500 font-medium">Run not completed!</p>
            </div>
          ) : (
            <div className="space-y-2">
            {/* Download Profile Report */}
            <Button
              onClick={() => { getDownloadReport(sortedrunlistData.run_id, 2); }}
              variant="primary"
              size="sm"
              fullWidth
              icon={<InboxArrowDownIcon className="h-4 w-4" />}
              className="justify-start text-sm"
            >
              Download Profile Report
            </Button>

            {/* View Profile Report */}
            <Button
              onClick={() => {
                window.location.href = `/home/<USER>
              }}
              variant="outline"
              size="sm"
              fullWidth
              icon={<EyeIcon className="h-4 w-4" />}
              className="justify-start text-sm"
            >
              View Profile Report
            </Button>

            {/* Download Shortlist Report */}
            <Button
              onClick={() => { getDownloadReport(sortedrunlistData.run_id, 3); }}
              variant="primary"
              size="sm"
              fullWidth
              icon={<InboxArrowDownIcon className="h-4 w-4" />}
              className="justify-start text-sm"
            >
              Download Shortlist Report
            </Button>

            {/* View Shortlist Report */}
            <Button
              onClick={() => {
                window.location.href = `/home/<USER>
              }}
              variant="outline"
              size="sm"
              fullWidth
              icon={<EyeIcon className="h-4 w-4" />}
              className="justify-start text-sm"
            >
              View Shortlist Report
            </Button>

            {/* Download Final Report */}
            <Button
              onClick={() => { getDownloadReport(sortedrunlistData.run_id, 4); }}
              variant="secondary"
              size="sm"
              fullWidth
              icon={<InboxArrowDownIcon className="h-4 w-4" />}
              className="justify-start text-sm"
            >
              Download Final Report
            </Button>

            {/* Delete Run */}
            <Button
              onClick={() => setShowDeleteModal(true)}
              variant="error"
              size="sm"
              fullWidth
              icon={<TrashIcon className="h-4 w-4" />}
              className="justify-start text-sm"
            >
              Delete Run
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};

export default SimplifiedRunListCard;
