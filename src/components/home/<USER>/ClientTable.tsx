"use client"
import React, { useState } from 'react';
import { useGetAllClientsWithUserId } from '@/hooks/home/<USER>/useGetAllClientsWithUserId';
import { getUser } from "@/api/user.localStorage"

const ClientTable = () => {
  // Sample data

  const user = getUser();
  const { data: clientData, isLoading } = useGetAllClientsWithUserId(user?.user_id);

  // State to store the selected client_id
  const [selectedClientId, setSelectedClientId] = useState(null);

  // Handle row selection
  const handleRowClick = (clientId) => {
    setSelectedClientId(clientId);
  };

  return (
    <div className='h-screen lg:h-full w-full rounded-lg p-4 '>
      <h2 className='text-2xl font-semibold text-slate-700 mb-4'>Client Table</h2>

      <div className='overflow-auto h-[70%]'>
        <table className="min-w-full table-auto bg-white rounded-lg shadow-md ">
          <thead className="bg-slate-500 text-white h-fit">
            <tr>
              <th className="px-4 py-2">Client ID</th>
              <th className="px-4 py-2">User ID</th>
              <th className="px-4 py-2">Name</th>
              <th className="px-4 py-2">Created By</th>
              <th className="px-4 py-2">Description</th>
              <th className="px-4 py-2">Creation Date</th>
            </tr>
          </thead>
          <tbody>
            {clientData?.map((client) => (
              <tr
                key={client.user_id}
                onClick={() => handleRowClick(client.client_id)}
                className={`cursor-pointer hover:bg-gray-100 ${selectedClientId == client.client_id ? 'bg-slate-200' : 'bg-white'}`}
              >
                <td className="border px-4 py-2">{client.client_id}</td>
                <td className="border px-4 py-2">{client.user_id}</td>
                <td className="border px-4 py-2">{client.name}</td>
                <td className="border px-4 py-2">{client.created_by}</td>
                <td className="border px-4 py-2">{client.description}</td>
                <td className="border px-4 py-2">{new Date(client.creation_date).toLocaleString()}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="mt-4">
        {selectedClientId ? (
          <p className="text-slate-700">Selected Client ID: {selectedClientId}</p>
        ) : (
          <p className="text-slate-700">No Client Selected</p>
        )}
      </div>
    </div>
  );
};

export default ClientTable;
