"use client"
import React, { useEffect, useState } from 'react'
import AskModal from '@/components/ui/extra/AskModal';
import { CardBody, CardContainer, CardItem } from "@/components/ui/3d-card";
import { Label } from '@/components/ui/label';
import { AnimatePresence, motion } from "framer-motion";
import { Input } from '@/components/ui/input';
import { CanvasRevealEffect } from '@/components/ui/canvas-reveal-effect';
import { useRemoveProject } from '@/hooks/home/<USER>/useRemoveProject';
import { tree } from 'next/dist/build/templates/app-page';


// Define the interface for the props
interface ProjectDetails {
  project_id: number;
  project_name: string;
  project_description: string;
  creation_date: string
  selectedClientId: number;
  client_name: string;
  run_is_present: boolean;
}

const ProjectCard: React.FC<ProjectDetails> = ({
  project_id,
  project_name,
  project_description,
  client_name,
  creation_date,
  run_is_present,
  selectedClientId }) => {

  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const deleteProject = useRemoveProject(project_id)

  const handleDeleteProject = async () => {
    const response = await deleteProject.mutateAsync()

    if (response.msg) {

    }
    else if (response.err) {
      // setDisplayResponse(true)
    }
  }


  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };
  const [hovered, setHovered] = useState(false)


  return (
    <div
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {showDeleteModal && (
        <AskModal
          questionToAsk="Do you want to continue?"
          questionDetail="Deleting the client cannot be undone."
          onYes={handleDeleteProject}
          onNo={closeDeleteModal}
          closeModal={closeDeleteModal}
        />
      )}
      <CardContainer className="inter-var shadow-lg bg-gradient-to-t from-gray-950 to-[84%] rounded-2xl">
        {hovered && <AnimatePresence>
          <div className="h-full w-full absolute inset-0">
            <CanvasRevealEffect
              animationSpeed={3}
              containerClassName="bg-transparent"
              colors={[[125, 211, 252]]}
              dotSize={2}
            />
          </div>
        </AnimatePresence>}
        <CardBody className="relative group/card  dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-black dark:border-white/[0.2] border-slate-500 w-auto sm:w-[30rem] lg:w-[5J00px] rounded-xl p-6 border-[1px] h-fit hover:bg-transparent">
          <div className='flex justify-between w-full flex-col lg:flex-row'>
            <div className='w-full h-fit flex flex-col justify-between items-center'>
              <div className='w-full'>
                <CardItem translateZ="100" className="w-full mt-4">
                  <div className='w-full h-fit flex justify-between'>
                    <Label className='text-[12px] text-slate-200 font-semibold'>Project Name: </Label>
                    <div className='w-fit h-fit flex gap-3 text-[12px] text-slate-200'>
                      <span className='font-semibold'>Type:</span> AI Interview
                    </div>
                  </div>
                  <Input
                    type="text"
                    placeholder="Project Name"
                    value={project_name || "Project Name"}
                    className='text-slate-200 bg-transparent text-[20px]'
                    disabled={true}
                  // onChange={(e) => setClientName(e.target.value)}
                  // disabled={!editable}   // Disable when editable is false
                  />
                </CardItem>
                <CardItem translateZ="100" className="w-full mt-4">
                  <Label className='text-[12px] text-slate-200'>Project Description: </Label>
                  <Input
                    placeholder="Project Description"
                    value={project_description || "Project Description"}
                    className='text-slate-200 bg-transparent text-[20px]'
                    disabled={true}
                  // onChange={(e) => setClientDescription(e.target.value)}
                  // disabled={!editable} // Disable when editable is false
                  />
                </CardItem>
              </div>
              <div className='flex gap-10 w-full justify-between mt-4'>
                <CardItem
                  translateZ="50"
                  className="text-xs text-slate-200 dark:text-slate-200"
                >
                  <span>Client Name: </span>
                  <span>{client_name}</span>
                </CardItem>
                <CardItem
                  translateZ="50"
                  className="text-xs text-slate-200 dark:text-slate-200"
                >
                  <span>Creation Date: </span>
                  <span>
                    {new Date(creation_date).toLocaleString()}
                  </span>
                </CardItem>
              </div>
            </div>
          </div>
          <div className="flex justify-center items-center mt-4 gap-4 w-full">
            <CardItem
              translateZ={20}
              className="text-[13px] lg:text-sm text-slate-200 py-[7.5px] px-2 rounded-md"
              onClick={() => {
                window.location.href = `/home/<USER>/projectlist/${project_id}`;
              }}
            >
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.8 }}
                disabled={!run_is_present}
                className="p-[3px] relative"
                onClick={() => {
                  window.location.href = `/home/<USER>/projectlist/${project_id}`;
                }}>
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full" />
                <div className="px-8 py-2  bg-black rounded-full relative group transition duration-200 text-white hover:bg-transparent">
                  Run list
                </div>
              </motion.button>
            </CardItem>
            <CardItem
              translateZ={20}
              className="text-[13px] lg:text-sm text-slate-200 py-[7.5px] px-2 rounded-md"
              onClick={() => window.location.href = `/home/<USER>
            >
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.8 }}
                className="p-[3px] relative">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full" />
                <div className="px-8 py-2  bg-black rounded-full relative group transition duration-200 text-white hover:bg-transparent">
                  Create Run
                </div>
              </motion.button>
            </CardItem>
            <CardItem
              translateZ={20}
            >
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.8 }}
                className={`text-[13px] lg:text-sm text-slate-200 bg-red-800 py-[7.5px] px-4 rounded-full hover:bg-red-600 duration-75 z-50`}
                onClick={() => {
                  // handleDeleteClientPopUp()
                  setShowDeleteModal(true)
                }}
              >
                Delete
              </motion.button>
            </CardItem>



          </div>
        </CardBody>
      </CardContainer>
    </div>
  )
}

export default ProjectCard