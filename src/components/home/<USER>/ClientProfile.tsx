"use client"
import React, { useState } from 'react'

const ClientProfile = () => {
    const [state, setState] = useState('Add');
    const [clientName, setClientName] = useState('');
    const [clientDescription, setClientDescription] = useState('');
    const [file, setFile] = useState<File | null>(null);
    const [errors, setErrors] = useState({
        name: '',
        description: '',
        file: '',
    });

    const handlePostClientData = () => {
        if (!clientName || !clientDescription || !file) {
            setErrors({
                name: clientName ? '' : 'Client Name is required',
                description: clientDescription ? '' : 'Description is required',
                file: file ? '' : 'Logo is required',
            });
            return;
        }


        // Clear form after successful submission
        setClientName('');
        setClientDescription('');
        setFile(null);
        setErrors({
            name: '',
            description: '',
            file: '',
        });
    };

    return (
        <div className='h-screen lg:h-full w-full  rounded-lg flex justify-center items-center'>
            <div className='h-full w-full flex flex-col p-8 justify-center items-center gap-8'>

                {/* Make it toggle button */}
                <div className="relative w-[90%] h-[40px] bg-slate-300 rounded-md flex">
                    {/* Active background indicator */}
                    <div
                        className={`absolute top-0 left-0 h-full w-1/2 bg-secondary text-white rounded-md transition-transform duration-300 ease-in-out ${state === 'Edit' ? 'translate-x-full' : ''}`}
                    ></div>

                    {/* Toggle Buttons */}
                    <button
                        className={`z-10 flex-1 text-base lg:text-xl py-2 transition duration-300 ease-in-out ${state === 'Add' ? 'bg-secondary text-white rounded-md' : 'bg-white text-slate-700 rounded-md'}`}
                        onClick={() => setState('Add')}
                    >
                        Add Client
                    </button>

                    <button
                        className={`z-10 flex-1 text-base lg:text-xl py-2 transition duration-300 ease-in-out ${state === 'Edit' ? 'bg-secondary text-white rounded-md' : 'bg-white text-slate-700 rounded-md'}`}
                        onClick={() => setState('Edit')}
                    >
                        Edit Client
                    </button>
                </div>
                {/* Conditional form rendering based on state */}
                {state === 'Add' ? (
                    <div className="flex flex-col justify-between items-center relative bg-secondary p-5 rounded-lg lg:w-[300px] lg:h-[450px] xl:h-[550px] xl:w-[400px]">
                        <h2 className="text-2xl font-semibold text-white mb-4 text-center">Add Client</h2>

                        {/* Client Name Input */}
                        <div className="relative w-full mb-3">
                            <input
                                type="text"
                                id="client_name"
                                value={clientName}
                                onChange={(e) => setClientName(e.target.value)}
                                className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-white bg-transparent rounded-lg border border-gray-300 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                                placeholder=" "
                            />
                            <label
                                htmlFor="client_name"
                                className="absolute text-sm text-white bg-secondary duration-300 transform -translate-y-4 scale-75 top-2 z-10 px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                            >
                                Client Name
                            </label>
                            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                        </div>

                        {/* Client Description Textarea */}
                        <div className="relative w-full mb-3">
                            <textarea
                                id="description"
                                value={clientDescription}
                                onChange={(e) => setClientDescription(e.target.value)}
                                rows={3}
                                className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-white bg-transparent rounded-lg border border-gray-300 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                                placeholder=" "
                            ></textarea>
                            <label
                                htmlFor="description"
                                className="absolute text-sm bg-secondary text-white duration-300 transform -translate-y-4 scale-75 top-2 z-10 px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                            >
                                Description
                            </label>
                            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
                        </div>

                        {/* File Upload Input */}
                        <div className="relative w-full mb-3">
                            <label className="block mb-2 text-sm font-medium text-white" htmlFor="logo">
                                Upload Logo
                            </label>
                            <input
                                className="block w-full p-1 text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-white focus:outline-none"
                                id="logo"
                                type="file"
                                accept="image/*"
                                onChange={(e) => setFile(e.target.files?.[0] || null)}
                            />
                            {errors.file && <p className="text-red-500 text-sm mt-1">{errors.file}</p>}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            onClick={handlePostClientData}
                            className="w-full text-secondary bg-white hover:bg-blue-300 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
                        >
                            Submit
                        </button>
                    </div>
                ) : (
                    <div className="flex flex-col justify-between items-center relative bg-secondary p-5 rounded-lg lg:w-[300px] lg:h-[450px] xl:h-[550px] xl:w-[400px]">
                        <h2 className="text-2xl font-semibold text-white mb-4 text-center">Edit Client</h2>

                        {/* Client Name Input */}
                        <div className="relative w-full mb-3">
                            <input
                                type="text"
                                id="client_name"
                                value={clientName}
                                onChange={(e) => setClientName(e.target.value)}
                                className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-white bg-transparent rounded-lg border border-gray-300 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                                placeholder=" "
                            />
                            <label
                                htmlFor="client_name"
                                className="absolute text-sm text-white bg-secondary duration-300 transform -translate-y-4 scale-75 top-2 z-10 px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                            >
                                Client Name
                            </label>
                            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                        </div>

                        {/* Client Description Textarea */}
                        <div className="relative w-full mb-3">
                            <textarea
                                id="description"
                                value={clientDescription}
                                onChange={(e) => setClientDescription(e.target.value)}
                                rows={3}
                                className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-white bg-transparent rounded-lg border border-gray-300 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                                placeholder=" "
                            ></textarea>
                            <label
                                htmlFor="description"
                                className="absolute text-sm bg-secondary text-white duration-300 transform -translate-y-4 scale-75 top-2 z-10 px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                            >
                                Description
                            </label>
                            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
                        </div>

                        {/* File Upload Input */}
                        <div className="relative w-full mb-3">
                            <label className="block mb-2 text-sm font-medium text-white" htmlFor="logo">
                                Upload Logo
                            </label>
                            <input
                                className="block w-full p-1 text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-white focus:outline-none"
                                id="logo"
                                type="file"
                                accept="image/*"
                                onChange={(e) => setFile(e.target.files?.[0] || null)}
                            />
                            {errors.file && <p className="text-red-500 text-sm mt-1">{errors.file}</p>}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            onClick={handlePostClientData}
                            className="w-full text-secondary bg-white hover:bg-blue-300 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
                        >
                            Submit
                        </button>
                    </div>
                )}

            </div>
        </div>
    )
}

export default ClientProfile