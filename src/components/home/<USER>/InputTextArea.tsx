"use client";
import React, { useState } from "react";

import {
  InformationCircleIcon,
  PencilSquareIcon,
} from "@heroicons/react/24/outline";
import { Textarea } from "@/components/ui/Textarea";
import { Label } from "@/components/ui/label";

interface InputTextAreaProps {
  setInfo: (value: string) => void;  // setter function to update the info state
  info: string;  // value to be displayed in the textarea
}

export default function InputTextArea({ setInfo, info }: InputTextAreaProps) {

  const [infoDetails, setinfoDetails] = useState(info)

  const handleInfoChanges = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInfo(e.target.value)
    setinfoDetails(e.target.value)
  }

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-3">
          <PencilSquareIcon className="w-4 h-4 text-green-600" />
          <h3 className="text-sm font-semibold text-gray-900">
            Additional Context
          </h3>
        </div>

        <div className="space-y-3">
          <div className="bg-green-50 rounded-lg p-2 border border-green-100">
            <p className="text-xs text-green-800">
              <strong>Optional:</strong> Add specific requirements or preferences to guide the assessment process.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="info" className="text-xs font-medium text-gray-700">
              Assessment Instructions
            </Label>
            <Textarea
              className="w-full min-h-[80px] resize-none border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-lg text-xs"
              name="info"
              value={infoDetails}
              onChange={(e) => handleInfoChanges(e)}
              id="info"
              placeholder="Example: Focus on technical skills, prioritize startup experience, look for leadership qualities, consider remote work experience..."
            />
            <div className="text-xs text-gray-500">
              {infoDetails.length} characters
            </div>
          </div>

          {/* Additional guidance */}
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <h4 className="text-xs font-semibold text-gray-900 mb-2">What to include:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700">
              <div>
                <h5 className="font-medium text-gray-800 mb-1">Skills & Experience</h5>
                <ul className="space-y-0.5 text-xs">
                  <li>• Required technical skills</li>
                  <li>• Years of experience needed</li>
                  <li>• Industry background</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium text-gray-800 mb-1">Preferences</h5>
                <ul className="space-y-0.5 text-xs">
                  <li>• Education requirements</li>
                  <li>• Soft skills priorities</li>
                  <li>• Cultural fit factors</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Examples section */}
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
            <h4 className="text-xs font-semibold text-blue-900 mb-2">Example Instructions:</h4>
            <div className="space-y-2 text-xs text-blue-800">
              <div className="bg-white rounded p-2 border border-blue-200">
                <strong>Software Developer:</strong> "Prioritize candidates with 3+ years React experience, strong problem-solving skills, and startup environment adaptability."
              </div>
              <div className="bg-white rounded p-2 border border-blue-200">
                <strong>Marketing Manager:</strong> "Focus on digital marketing expertise, campaign management experience, and data-driven decision making abilities."
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}