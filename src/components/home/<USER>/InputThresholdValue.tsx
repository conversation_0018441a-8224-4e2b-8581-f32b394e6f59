"use client";
import React, { useState } from "react";
import { getDownloadReport } from '@/api/home/<USER>/download/getDownloadReport';
import {
  MinusCircleIcon,
  PlusCircleIcon,
} from "@heroicons/react/20/solid";
import {
  SparklesIcon,
  CheckBadgeIcon,
} from "@heroicons/react/24/outline";

import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import { useGenerateReport } from "@/hooks/home/<USER>/useGenerateReport";

interface InputThresholdValueProps {
  handleGenerateReport: () => void;
  setThresholdNumber: React.Dispatch<React.SetStateAction<number>>;
  thresholdNumber: number;
  fileNameCV?: string;
  isLoading: boolean;
  isReportGenerated: boolean;
  selectedRunId: number;
  fileNameZip?: string;
  projectId: number;
  useDefaultWeights?: any;
  rangeValuesrange1: any;
  rangeValuesrange2: any;
  info?: any;
  filecount?: any;
}

const InputThresholdValue: React.FC<InputThresholdValueProps> = ({
  setThresholdNumber,
  info,
  useDefaultWeights,
  rangeValuesrange1,
  rangeValuesrange2,
  thresholdNumber,
  fileNameCV,
  selectedRunId,
  fileNameZip,
  projectId,
  filecount
}) => {
  const [thresholdNumberDetails, setThresholdNumberDetails] = useState<number>(filecount || 1)
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isReportGenerated, setIsReportGenerated] = useState<boolean>(false);

  const generateReportData = useGenerateReport(
    selectedRunId,
    useDefaultWeights,
    rangeValuesrange1,
    rangeValuesrange2,
    thresholdNumberDetails,
    info
  );

  const handleSetThresholdNumberDetails = (numberValue: number) => {
    setThresholdNumberDetails(numberValue)
    setThresholdNumber(numberValue)
  }

  const handleGenerateReport = async () => {
    // Set loading to true when report generation starts
    setIsLoading(true);

    try {
      // Trigger the mutation to upload the data
      const response = await generateReportData.mutateAsync();

      if (response.msg) {
        setIsReportGenerated(true);
      }

      if (response.err) {
        console.error("Error:", response.err);
        setIsReportGenerated(false);
      }

      if (response.detail) {
        console.error("Detail error:", response.detail);
        setIsReportGenerated(false);
      }

      // Check if the response has the 'success' property
      if (response && response.success) {
        console.log("Success:", response.success);
        setIsReportGenerated(true);
      } else if (!response.msg && !response.err && !response.detail) {
        setIsLoading(false);
        console.error("Unexpected response format", response);
      }
    } catch (error) {
      console.error("Error generating report:", error);
      // alert("An error occurred while generating the report. Please try again.");
    } finally {
      // Stop loading regardless of success or failure
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="w-full max-w-4xl mx-auto">

        {!fileNameCV && (
          <Card className="mb-4">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg">
                  Generate Assessment Report
                </CardTitle>
                <div className="px-2 py-0.5 bg-success-100 text-success-800 text-xs font-medium rounded-full">
                  {filecount} CVs
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">

              {/* Process Overview */}
              <div className="bg-primary-50 rounded-lg p-3 border border-primary-100 mb-4">
                <h4 className="text-sm font-semibold text-primary-900 mb-2">What happens next:</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-xs text-primary-800">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                    <span>AI analyzes all CVs against job requirements</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                    <span>Candidates ranked by assessment criteria</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                    <span>Detailed reports generated with insights</span>
                  </div>
                </div>
              </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-900">Select Shortlist Size</h4>
                    <p className="text-xs text-gray-600">
                      Choose how many top-ranked candidates to include in your shortlist from {filecount} total CVs
                    </p>

                    {/* Shortlist guidance */}
                    <div className="bg-yellow-50 rounded p-2 border border-yellow-200">
                      <p className="text-xs text-yellow-800">
                        <strong>Tip:</strong> Industry standard is 3-5 candidates per position for optimal interview efficiency.
                      </p>
                    </div>

                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center justify-center gap-3">
                        {/* Decrement Button */}
                        <Button
                          onClick={() => handleSetThresholdNumberDetails(thresholdNumberDetails > 1 ? thresholdNumberDetails - 1 : 1)}
                          variant="primary"
                          size="sm"
                          className="p-2"
                        >
                          <MinusCircleIcon className="w-4 h-4" />
                        </Button>

                        {/* Input Display */}
                        <div className="flex flex-col items-center pt-5">
                          <Input
                            type="number"
                            min="1"
                            max={filecount}
                            value={thresholdNumberDetails}
                            onChange={(e) => handleSetThresholdNumberDetails(Number(e.target.value))}
                            className="text-center text-md font-semibold w-16 h-10"
                          />
                          <span className="text-xs text-gray-500 mt-1">candidates</span>
                        </div>

                        {/* Increment Button */}
                        <Button
                          onClick={() => handleSetThresholdNumberDetails(thresholdNumberDetails < filecount ? thresholdNumberDetails + 1 : filecount)}
                          variant="primary"
                          size="sm"
                          className="p-2"
                        >
                          <PlusCircleIcon className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="text-center">
                        <span className="text-xs font-medium text-gray-700">
                          {thresholdNumberDetails} of {filecount} ({Math.round((thresholdNumberDetails / filecount) * 100)}%)
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Assessment Summary</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">Total CVs</span>
                        <span className="font-bold text-primary-600">{filecount}</span>
                      </div>
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">Shortlist Size</span>
                        <span className="font-bold text-success-600">{thresholdNumberDetails}</span>
                      </div>
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">Selection Rate</span>
                        <span className="font-bold text-primary-600">{Math.round((thresholdNumberDetails / filecount) * 100)}%</span>
                      </div>
                    </div>
                  </div>

                  {/* Report types explanation */}
                  <div className="bg-primary-50 rounded-lg p-3 border border-primary-100">
                    <h4 className="text-sm font-semibold text-primary-900 mb-2">Report Types Available:</h4>
                    <div className="space-y-2 text-xs text-primary-800">
                      <div className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-1.5"></div>
                        <div>
                          <strong>Shortlist Report:</strong> Top {thresholdNumberDetails} candidates with detailed scores
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-1.5"></div>
                        <div>
                          <strong>Complete Report:</strong> All {filecount} candidates with comprehensive analysis
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-1.5"></div>
                        <div>
                          <strong>Assessment Package:</strong> Everything including raw data and insights
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Processing time estimate */}
                  <div className="bg-success-50 rounded-lg p-3 border border-success-100">
                    <h4 className="text-sm font-semibold text-success-900 mb-1">Processing Time:</h4>
                    <p className="text-xs text-success-800">
                      Estimated {Math.ceil(filecount / 10)} minute(s) for {filecount} CVs
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Generate Button */}
        <div className="flex justify-center mt-4">
          <Button
            onClick={() => {
              if (isReportGenerated) {
                window.location.href = `/home/<USER>/projectlist/${projectId}`;
              } else {
                handleGenerateReport();
              }
            }}
            variant={isReportGenerated ? "success" : "primary"}
            size="lg"
            isLoading={isLoading}
            icon={!isLoading && !isReportGenerated ? <SparklesIcon className="w-4 h-4" /> : undefined}
            className="shadow-md hover:shadow-lg"
          >
            {isLoading
              ? "Generating Report..."
              : isReportGenerated
              ? "View Results"
              : "Generate Report"
            }
          </Button>
        </div>

        {/* Report Actions */}
        {isReportGenerated && (
          <Card className="mt-8 relative overflow-hidden" padding="lg" shadow="sm">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-success-50 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

            <div className="relative">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-success-600 rounded-full mb-4">
                  <CheckBadgeIcon className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl mb-3">
                  Assessment Report Generated Successfully! 🎉
                </CardTitle>
                <CardDescription className="text-lg">
                  Your comprehensive talent assessment report is ready. Download or view the detailed results below.
                </CardDescription>
              </div>

              <div className="space-y-6">
                {fileNameCV ? (
                  <div className="bg-primary-50 rounded-xl p-6 border border-primary-100">
                    <h4 className="text-lg font-semibold text-primary-900 mb-4">Individual Assessment Report</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <Button
                        onClick={() => { getDownloadReport(selectedRunId, 2); }}
                        variant="primary"
                        size="md"
                        className="shadow-lg hover:shadow-xl"
                      >
                        📄 Download Report
                      </Button>
                      <Link
                        href={`/home/<USER>
                        rel="noopener noreferrer"
                        target="_blank"
                        className="px-6 py-3 bg-white hover:bg-gray-50 text-primary-600 font-medium rounded-lg border-2 border-primary-600 hover:border-primary-700 transition-all duration-200 text-center shadow-lg hover:shadow-xl flex items-center justify-center"
                      >
                        👁️ View Report Online
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-primary-50 rounded-xl p-6 border border-primary-100">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                        <h4 className="text-lg font-semibold text-primary-900">Shortlist Report</h4>
                      </div>
                      <p className="text-sm text-primary-700 mb-4">
                        Top {thresholdNumberDetails} candidates ranked by assessment score
                      </p>
                      <div className="space-y-3">
                        <Button
                          onClick={() => { getDownloadReport(selectedRunId, 3); }}
                          variant="primary"
                          size="md"
                          fullWidth
                          className="shadow-lg hover:shadow-xl"
                        >
                          📋 Download Shortlist
                        </Button>
                        <Link
                          href={`/home/<USER>
                          rel="noopener noreferrer"
                          target="_blank"
                          className="block w-full px-4 py-3 bg-white hover:bg-gray-50 text-primary-600 font-medium rounded-lg border-2 border-primary-600 hover:border-primary-700 transition-all duration-200 text-center shadow-lg hover:shadow-xl"
                        >
                          👁️ View Shortlist Online
                        </Link>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                        <h4 className="text-lg font-semibold text-gray-900">Complete Report</h4>
                      </div>
                      <p className="text-sm text-gray-700 mb-4">
                        Detailed analysis of all {filecount} candidates with scores and insights
                      </p>
                      <div className="space-y-3">
                        <Button
                          onClick={() => { getDownloadReport(selectedRunId, 2); }}
                          variant="secondary"
                          size="md"
                          fullWidth
                          className="shadow-lg hover:shadow-xl"
                        >
                          📊 Download Full Report
                        </Button>
                        <Link
                          href={`/home/<USER>
                          rel="noopener noreferrer"
                          target="_blank"
                          className="block w-full px-4 py-3 bg-white hover:bg-gray-50 text-gray-600 font-medium rounded-lg border-2 border-gray-300 hover:border-gray-400 transition-all duration-200 text-center shadow-lg hover:shadow-xl"
                        >
                          👁️ View Full Report Online
                        </Link>
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-success-50 rounded-xl p-6 border border-success-200">
                  <div className="text-center">
                    <h4 className="text-lg font-semibold text-success-900 mb-3">🎁 Complete Assessment Package</h4>
                    <p className="text-sm text-success-700 mb-4">
                      Download everything: reports, candidate profiles, assessment data, and analysis summaries
                    </p>
                    <Button
                      onClick={() => { getDownloadReport(selectedRunId, 4); }}
                      variant="success"
                      size="lg"
                      className="shadow-lg hover:shadow-xl"
                    >
                      📦 Download Complete Package
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default InputThresholdValue;