"use client"
import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { InformationCircleIcon } from "@heroicons/react/24/outline";
import LabelInputContainer from '@/components/ui/LabelInputContainer';
import BottomGradient from '@/components/ui/BottomGradient';
import SubmitTypeButton from "@/components/ui/SubmitTypeButton"

interface ProjectFormProps {
    projectName: string;
    setProjectName: React.Dispatch<React.SetStateAction<string>>;
    description: string;
    setDescription: React.Dispatch<React.SetStateAction<string>>;
    errors: { projectName: string; description: string };
    handleSubmit: () => void;
}

const ProjectForm: React.FC<ProjectFormProps> = ({
    projectName,
    setProjectName,
    description,
    setDescription,
    errors,
    handleSubmit,
}) => {
    return (
        <div className="flex items-start md:items-center justify-center w-full h-full py-2 px-4 lg:py-12">
            <div className='w-full lg:w-8/12 h-fit lg:h-[90%] bg-white flex flex-col lg:flex-row justify-center items-center rounded-lg p-4'>
                {/* Left */}
                <div className='w-[50%] lg:p-2 h-full flex justify-center'
                    style={{
                        backgroundImage: `url(/project/project.svg)`,
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: 'cover',
                    }}></div>

                {/* Right */}
                <div className='w-full lg:w-[50%] px-4 h-full flex-1 flex flex-col justify-center items-center'>
                    <div className='w-full flex flex-col lg:flex-row h-fit justify-between items-start py-2'>
                        <h1 className='text-2xl w-full h-[40px]'>Project Form</h1>
                        <select className='shadow-lg h-[40px] border-none rounded-[8px] text-slate-800 bg-slate-50 '>
                            <option value="someOption">Both</option>
                            <option value="someOption">Deep Screen</option>
                            <option value="otherOption">Ai Interview</option>
                        </select>
                    </div>

                    <LabelInputContainer className="relative w-full mb-4">
                        <Input
                            type="text"
                            id="project_name"
                            className='shadow-lg h-[60px]'
                            value={projectName}
                            onChange={(e) => setProjectName(e.target.value)}
                            placeholder=""
                        />
                        <Label
                            htmlFor="project_name"
                            className="absolute text-sm text-gray-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1 flex gap-2 "
                        >
                            Project Name
                            <div className="relative group">
                                <InformationCircleIcon className="w-5 h-5 text-gray-400" />
                                <div className="absolute bottom-0 left-0 w-max mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-sm rounded-md">
                                    Please enter the name for the project
                                </div>
                            </div>
                        </Label>
                        {errors.projectName && (
                            <span className="text-red-500 text-sm">{errors.projectName}</span>
                        )}
                    </LabelInputContainer>

                    <LabelInputContainer className="relative w-full mb-4">
                        <Input
                            id="description"
                            rows={3}
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className='h-[200px] shadow-lg '
                            placeholder=""
                        />
                        <Label
                            htmlFor="description"
                            className="absolute text-sm text-gray-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1 flex gap-2"
                        >
                            Project Description
                            <div className="relative group">
                                <InformationCircleIcon className="w-5 h-5 text-gray-400" />
                                <div className="absolute bottom-0 left-0 w-max mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-sm rounded-md">
                                    Please enter the description for the project
                                </div>
                            </div>
                        </Label>

                        {errors.description && (
                            <span className="text-red-500 text-sm">{errors.description}</span>
                        )}
                    </LabelInputContainer>

                    <div className='w-full h-fit'>
                        <SubmitTypeButton onClick={handleSubmit}>
                            Submit &rarr;
                        </SubmitTypeButton>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProjectForm;
