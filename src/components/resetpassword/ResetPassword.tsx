"use client"
import React ,{useState} from 'react'

const ResetPassword = () => {

    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    const handleSubmit = (e) => {
        e.preventDefault();
        if (password === confirmPassword) {
          useResetPassword(password, token, email); // Call the reset function
          alert('Password reset successfully!');
          // Perform additional actions like redirecting the user
        } else {
          alert('Passwords do not match!');
        }
      };
  return (
    <div className='bg-white p-8 rounded shadow-lg w-96'>
          <h1 className='text-2xl font-bold mb-6 text-center'>Reset Password</h1>
          <form onSubmit={handleSubmit}>
            <div className='mb-4'>
              <label className='block text-gray-700 text-sm font-bold mb-2' htmlFor='new-password'>
                New Password
              </label>
              <input
                type='password'
                id='new-password'
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <div className='mb-6'>
              <label className='block text-gray-700 text-sm font-bold mb-2' htmlFor='confirm-password'>
                Confirm Password
              </label>
              <input
                type='password'
                id='confirm-password'
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
            </div>
            <button
              type='submit'
              className='w-full bg-secondary text-white py-2 rounded-md hover:bg-hoverColor transition-colors'
            >
              Reset Password
            </button>
          </form>
        </div>
  )
}

export default ResetPassword