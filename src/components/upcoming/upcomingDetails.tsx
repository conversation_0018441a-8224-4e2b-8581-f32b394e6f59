import React from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

// Define the interface for props
interface UpcomingDetailsProps {
    isOpen: boolean;
    closeModal: () => void;
    title?: string;           // Optional title
    features?: string[];      // Optional features array
    benefits?: string[];      // Optional benefits array
}

const UpcomingDetails: React.FC<UpcomingDetailsProps> = ({
    isOpen,
    closeModal,
    title = "Default Title",      // Default value for title
    features = [],               // Default empty array for features
    benefits = [],               // Default empty array for benefits
}) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-500 bg-opacity-50 z-50">
            <div className="bg-white rounded-[20px] p-6 md:w-[600px] md:h-[500px] lg:w-[800px] lg:h-[600px] shadow-lg overflow-y-auto">
                {/* Header Section */}
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-semibold text-gray-800">{title}</h2>
                    <button
                        onClick={closeModal}
                        className="text-gray-500 hover:text-gray-700 p-2 rounded-full"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                {/* Body Section */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-700">Features:</h3>
                    <ul className="list-disc pl-5 text-gray-600 space-y-2">
                        {features.length > 0 ? (
                            features.map((feature, index) => (
                                <li key={index}>{feature}</li>
                            ))
                        ) : (
                            <li>No features available</li>
                        )}
                    </ul>
                </div>

                {/* Benefits Section */}
                <div className="mt-6">
                    <h3 className="text-lg font-semibold text-gray-700">Benefits:</h3>
                    <ul className="list-decimal pl-5 text-gray-600 space-y-2">
                        {benefits.length > 0 ? (
                            benefits.map((benefit, index) => (
                                <li key={index}>{benefit}</li>
                            ))
                        ) : (
                            <li>No benefits available</li>
                        )}
                    </ul>
                </div>

                {/* Footer Section */}
                {/* <div className="mt-6 flex items-center justify-end space-x-4">
                    <button
                        onClick={closeModal}
                        className="text-gray-500 hover:text-gray-700 px-4 py-2 rounded-lg focus:outline-none"
                    >
                        Close
                    </button>
                </div> */}
            </div>
        </div>
    );
};

export default UpcomingDetails;
