import React, { useState, useEffect, useRef } from "react";
import { XMarkIcon, FingerPrintIcon } from "@heroicons/react/24/outline";
import { useForgetPassword } from "@/hooks/user/useForgetPassword";

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ForgetPassword: React.FC<ProfileModalProps> = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [buttonText, setButtonText] = useState("Send Reset Link");
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);

  const { mutate, isLoading, isSuccess, isError, data } = useForgetPassword(email);
  const intervalRef = useRef<NodeJS.Timeout | null>(null); // Use useRef for the interval

  useEffect(() => {
    if (isLoading) {
      setButtonText("Loading");
      intervalRef.current = setInterval(() => {
        setButtonText((prev) => {
          if (prev === "Loading") return "Loading.";
          if (prev === "Loading.") return "Loading..";
          if (prev === "Loading..") return "Loading...";
          return "Loading";
        });
      }, 500);
    } else {
      clearInterval(intervalRef.current as NodeJS.Timeout); // Clear interval on stop loading
      if (isSuccess && data) {
        try {
          // Safe parsing of response message
          const parsedMessage = JSON.parse(data.message.replace(/'/g, '"').slice(0, -1));
          setButtonText(parsedMessage.msg || "Email Sent!");
        } catch (e) {
          console.error("Failed to parse response:", e);
          setButtonText("Email Sent!");
        }
      } else if (isError) {
        setButtonText("Failed to send email");
        setError("Failed to send email. Please try again.");
      } else {
        setButtonText("Send Reset Link");
      }
    }

    // Cleanup interval on unmount
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [isLoading, isSuccess, isError, data]);

  const handleSubmit = () => {
    if (!email) {
      return setError("Please enter an email address.");
    }
    if (!validateEmail(email)) {
      return setError("Please enter a valid email address.");
    }

    setError(""); // Clear any previous errors
    mutate(); // Trigger the mutation with the email
    setIsButtonDisabled(true); // Disable the button after clicking
  };

  // Basic email validation
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  return (
    <div className={`fixed inset-0 z-20 flex items-center justify-center ${isOpen ? "block" : "hidden"}`}>
      <div className="fixed inset-0 bg-black opacity-25" onClick={onClose}></div>
      <div className="relative bg-white w-full sm:w-1/2 lg:w-3/12 p-6 rounded-lg shadow-lg h-[460px] m-4 lg:m-0">
        <div className="flex w-full justify-end">
          <button onClick={onClose}>
            <XMarkIcon className="h-6 w-6 text-gray-600" />
          </button>
        </div>
        <div className="flex flex-col items-center gap-2">
          <FingerPrintIcon className="h-10 w-10" />
          <h2 className="text-2xl font-bold text-gray-800">Forgot Password?</h2>
          <p className="text-center lg:text-left">
            No worries, we'll send you reset instructions.
          </p>
          <div className="w-10/12 lg:w-9/12 pt-10 flex flex-col gap-4">
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-2 w-full rounded-md border py-1.5 px-1 shadow-sm focus:ring-indigo-600"
              placeholder="Enter your email"
            />
            <div className="flex justify-center">
              <button
                onClick={handleSubmit}
                className={`w-10/12 lg:w-9/12 rounded-md bg-secondary text-white px-3 py-2 mt-4 ${
                  isButtonDisabled ? "cursor-not-allowed opacity-80" : "hover:bg-indigo-600"
                }`}
                disabled={isButtonDisabled}
              >
                {buttonText}
              </button>
            </div>
          </div>
          {error && <p className="text-red-600 text-sm">{error}</p>}
        </div>
      </div>
    </div>
  );
};

export default ForgetPassword;
