"use client";
import { Fragment, useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { removeUser } from "@/api/user.localStorage";
import Link from 'next/link';
import { Disclosure, Menu, Transition } from "@headlessui/react";
import PDFTutorialModal from "@/components/ui/PDFTutorialModal"
import { useGetUserLogo } from '@/hooks/user/useGetUserLogo'
import { motion } from 'framer-motion';
import { useGetUserDetailsWithLogo } from '@/hooks/user/useGetUserDetailsWithLogo';
import {
  Bars3Icon,
  ChevronDownIcon,
  XMarkIcon,
  SparklesIcon,
  InformationCircleIcon

} from "@heroicons/react/24/outline";
import {
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/20/solid";
import { useLogoutAll } from "@/hooks/useLogoutAll";
import Timer from "./timer/Timer";
import { getUser } from "@/api/user.localStorage"

const navigation = [
  { name: "Home", href: "/home" },
  // { name: "Generator", href: "/home/<USER>" },

];

function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

export default function Header() {
  // State to track the active navigation item

  const [isOpen, setIsOpen] = useState(false);
  const [isOpenTutorial, setisOpenTutorial] = useState(false)
  const [isOpenProject, setisOpenProject] = useState(false)
  const [isOpenClient, setisOpenClient] = useState(false)
  const [isOpenbudget, setIsOpenbudget] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();
  const [enabled, setEnabled] = useState(false);
  const user = getUser();
  const [time, settime] = useState<number>()

  const { data: userDataInfo, isLoading, isError } = useGetUserDetailsWithLogo(user?.user_id);
  const { data: userLogoData } = useGetUserLogo(userDataInfo?.user_logo)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem("timedetails", 1400);
    }

  }, [])


  const getActiveItem = () => {
    const path = router.pathname ?? '';
    const activeNavItem = navigation.find((item) => path.startsWith(item.href));
    return activeNavItem ? activeNavItem.name : '';
  };

  const [activeItem, setActiveItem] = useState(getActiveItem());

  const toggleDropdownProject = () => { setisOpenProject(!isOpenProject); setActiveItem("project"); }
  const toggleDropdownClient = () => { setisOpenClient(!isOpenClient); setActiveItem("Client"); }
  const toggleTutorial = () => { setisOpenTutorial(!isOpenTutorial); setActiveItem("Tutorial"); }
  const toggleDropdownbudget = () => { setIsOpenbudget(!isOpenbudget); setActiveItem("budget"); }



  useEffect(() => {
    // Use optional chaining to safely access pathname and provide Link default empty string if it's undefined
    const path = router.pathname ?? '';

    const activeNavItem = navigation.find((item) => path.startsWith(item.href));
    if (activeNavItem) {
      setActiveItem(getActiveItem());
    }
  }, [router.pathname, getActiveItem]);


  // Update navigation items' current property based on active item
  // const navigation = initialNavigation.map((item) => ({
  //   ...item,
  //   current: item.name === activeItem,
  // }));

  const { data: logout } = useLogoutAll(enabled);
  const projectDropdownRef = useRef(null);
  const budgetDropdownRef = useRef(null);

  const closeDropdowns = () => {
    setIsOpen(false);
    setIsOpenbudget(false);
  };

  const handleLogout = () => {
    removeUser(); // Call removeUser to clear user data
    window.history.replaceState(null, null, "/");
    router.replace("/login"); // Redirect to login page
    setEnabled(true);
  };


  const handleItemClick = (itemName, href, e) => {
    e.preventDefault();
    setActiveItem(itemName);

    router.push(href);
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (isOpen && projectDropdownRef.current && !projectDropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleMouseLeaveFn = () => {
    if (window.innerWidth >= 786) {

      console.log("handleMouseLeaveFn")
      setisOpenProject(false)
      setisOpenClient(false)
      setActiveItem(null)
    } else { }
  };

  // Effect for handling clicks outside the "Module" dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (isOpenbudget && budgetDropdownRef.current && !budgetDropdownRef.current.contains(event.target)) {
        setIsOpenbudget(false);
      }
    }
    if (isOpenbudget) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpenbudget]);

  const handleOpenModal = () => setShowModal(true);
  const handleCloseModal = () => setShowModal(false);
  const pdfUrl = "public/tutorials/sample.pdf";

  return (
    <Disclosure as="nav" className="bg-transparent shadow-xl">
      {({ open }) => (
        <>
          <div className="mx-auto max-w-8xl px-2 lg:px-8 ">
            <PDFTutorialModal
              show={showModal}
              pdfUrl={pdfUrl}
              onClose={handleCloseModal}
            />
            <div className="relative flex h-20 items-center justify-between w-full">
              <div className="absolute inset-y-0 left-0 flex items-center lg:hidden">
                {/* Mobile menu button*/}
                <Disclosure.Button className="  relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-slate-100 hover:text-slate-700 duration-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                  <span className="absolute -inset-0.5" />

                  {open ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>
              <div className="flex flex-1 items-center justify-center lg:items-stretch lg:justify-start xl:gap-20">
                <div className="flex flex-shrink-0 items-center">
                  {/* Image or Title */}
                  <Link
                    href={"/home"}><button className="text-[30px] font-bold text-gray-900">HR-Math</button>
                  </Link>
                </div>
                <div className="hidden lg:ml-6 lg:block w-full">
                  <div className="flex space-x-4  justify-center">
                    {navigation.slice(0, 6).map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        onClick={(e) => handleItemClick(item.name, item.href, e)}
                        onMouseLeave={handleMouseLeaveFn}
                        className={classNames(
                          item.name === activeItem
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-100',
                          'group flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200'
                        )}
                        aria-current={item.name === activeItem ? 'page' : undefined}
                      >
                        {item.name}
                      </Link>
                    ))}
                    <div
                      onMouseLeave={handleMouseLeaveFn}
                      className="relative "
                      data-te-dropdown-ref
                      ref={projectDropdownRef}>
                      <button
                        className={classNames(
                          "project" === activeItem
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-100',
                          'group flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200'
                        )}
                        aria-current={"project" === activeItem ? 'page' : undefined}
                        onClick={() => {
                          toggleDropdownProject();
                          setisOpenClient(false);
                        }}
                        id="dropdownMenuButton1"
                        aria-expanded={isOpenProject}
                        ref={projectDropdownRef}
                      >
                        Projects
                        <ChevronDownIcon className="ml-2 h-5 w-5" />
                      </button>
                      {isOpenProject && (
                        <ul
                          className="absolute z-50 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-200 divide-y divide-gray-100 focus:outline-none"
                          aria-labelledby="dropdownMenuButton1"
                        >
                          <li>
                            <Link
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                              href="/home/<USER>/create/null?clientId=null"
                            >
                              Create Project
                            </Link>
                          </li>
                          <li>
                            <Link
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                              href="/home/<USER>/projectlist"
                            >
                              Projects List
                            </Link>
                          </li>
                        </ul>
                      )}
                    </div>
                    <div
                      onMouseLeave={handleMouseLeaveFn}
                      className="relative "
                      data-te-dropdown-ref
                      ref={projectDropdownRef}>
                      <button
                        className={classNames(
                          "Client" === activeItem
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-100',
                          'group flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200'
                        )}
                        aria-current={"Client" === activeItem ? 'page' : undefined}
                        onClick={() => {
                          toggleDropdownClient();
                          setisOpenProject(false);
                        }}
                        id="dropdownMenuButton1"
                        aria-expanded={isOpenClient}
                        ref={projectDropdownRef}
                      >
                        Clients
                        <ChevronDownIcon className="ml-2 h-5 w-5" />
                      </button>

                      {isOpenClient && (
                        <ul
                          className="absolute z-50 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-200 divide-y divide-gray-100 focus:outline-none"
                          aria-labelledby="dropdownMenuButton1"
                        >
                          <li>
                            <Link
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                              href="/home"
                            >
                              Create Client
                            </Link>
                          </li>
                          <li>
                            <Link
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                              href="/home/<USER>"
                            >
                              Client List
                            </Link>
                          </li>
                        </ul>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 lg:static lg:inset-auto lg:ml-6 lg:pr-0 md:w-[280px] text-slate-700 justify-between">



                {/* Profile dropdown */}
                <div className="lg:block hidden">
                  <Timer handleLogout={handleLogout} timeDetail={1800} />
                </div>

                {/* Tutorial Code*/}
                {<div
                  onMouseLeave={handleMouseLeaveFn}
                  className="relative hidden sm:block"
                  data-te-dropdown-ref>
                  <button
                    className={classNames(
                      "tutorial" === activeItem
                        ? 'bg-blue-50 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100',
                      'group flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200'
                    )}
                    aria-current={"tutorial" === activeItem ? 'page' : undefined}
                    onClick={() => {
                      handleOpenModal()
                    }}
                    id="dropdownMenuButton1"
                    aria-expanded={isOpenTutorial}
                  >
                    Tutorial
                    <InformationCircleIcon className="ml-2 h-5 w-5" />
                  </button>

                </div>}

                <Menu as="div" className="relative ml-8">
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.8 }}
                    transition={{
                      duration: 0.4,
                      scale: { type: "spring", visualDuration: 0.4, bounce: 0.5 },
                    }}>
                    <Menu.Button className="relative flex rounded-full bg-gray-800 text-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                      <span className="absolute -inset-1.5" />
                      <Image
                        className="h-8 w-8 rounded-full"
                        src={(userDataInfo?.user_logo == null ? '/user.png' : userLogoData)}
                        alt="User"
                        width={40}
                        height={40}
                      />
                    </Menu.Button>
                  </motion.div>
                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Menu.Items
                      className="absolute right-0 z-50 mt-1 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-gray-200 divide-y divide-gray-100 focus:outline-none">
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            href="#"
                            className={classNames(
                              active ? "bg-gray-50" : "",
                              "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                            )}
                            onClick={() => {
                              window.location.href = `/home/<USER>
                            }}
                          >
                            <Cog6ToothIcon
                              className="h-5 w-5 mr-3 text-gray-500"
                              aria-hidden="true"
                            />
                            <span>Profile</span>
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            onClick={handleLogout}
                            className={classNames(
                              active ? "bg-gray-50" : "",
                              "flex items-center w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                            )}
                          >
                            <ArrowRightOnRectangleIcon
                              className="h-5 w-5 mr-3 text-gray-500"
                              aria-hidden="true"
                            />
                            <span>Logout</span>
                          </button>
                        )}
                      </Menu.Item>
                    </Menu.Items>
                  </Transition>
                </Menu>
                <motion.button
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.8 }}
                  transition={{
                    duration: 0.4,
                    scale: { type: "spring", visualDuration: 0.4, bounce: 0.5 }
                  }}
                  className="w-fit h-fit"
                  onClick={() => (router.push(`/upcoming`))}>
                  <SparklesIcon className="text-white ml-8 w-6 h-6" />
                </motion.button>
              </div>
            </div>
          </div>

          <Disclosure.Panel className="lg:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2 lg:hidden  ">
              <div className="block lg:hidden px-3 text-slate-300 text-base font-medium">
                <Timer handleLogout={handleLogout} timeDetail={1800} />
              </div>
              {navigation.slice(0, 6).map((item) => (
                <Disclosure.Button
                  key={item.name}
                  as="Link"
                  href={item.href}
                  className={classNames(
                    item.current
                      ? "bg-blue-50 text-blue-700"
                      : "text-gray-700 hover:bg-gray-50",
                    "block rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200"
                  )}
                  aria-current={item.current ? "page" : undefined}
                >
                  {item.name}
                </Disclosure.Button>
              ))}

              <div
                className="relative"
                data-te-dropdown-ref ref={projectDropdownRef}
                onMouseLeave={handleMouseLeaveFn}
              >
                <button
                  className="text-gray-700 hover:bg-gray-50 flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 w-full"
                  onClick={() => {
                    toggleDropdownProject();
                    setisOpenClient(false)
                  }}
                  id="dropdownMenuButton1"
                  aria-expanded={isOpenProject}
                  ref={projectDropdownRef}
                >
                  Projects
                  <ChevronDownIcon className="ml-2 h-5 w-5" />
                </button>
                {isOpenProject && (
                  <ul
                    className="absolute z-50 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-200 divide-y divide-gray-100 focus:outline-none"
                    aria-labelledby="dropdownMenuButton1"
                  >
                    <li>
                      <Link
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                        href="/home/<USER>/create/null?clientId=null"
                      >
                        Create Project
                      </Link>
                    </li>
                    <li>
                      <Link
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                        href="/home/<USER>/projectlist"
                      >
                        Project List
                      </Link>
                    </li>
                  </ul>
                )}
              </div>

              <div
                onMouseLeave={handleMouseLeaveFn}
                className="relative"
                data-te-dropdown-ref
                ref={projectDropdownRef}>
                <button
                  className="text-gray-700 hover:bg-gray-50 flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 w-full"
                  onClick={() => {
                    toggleDropdownClient();
                    setisOpenProject(false)
                  }}
                  id="dropdownMenuButton1"
                  aria-expanded={isOpenProject}
                  ref={projectDropdownRef}
                >
                  Clients
                  <ChevronDownIcon className="ml-2 h-5 w-5" />
                </button>
                {isOpenClient && (
                  <ul
                    className="absolute z-50 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-200 divide-y divide-gray-100 focus:outline-none"
                    aria-labelledby="dropdownMenuButton1"
                  >
                    <li>
                      <Link
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                        href="/home"
                      >
                        Create Client
                      </Link>
                    </li>
                    <li>
                      <Link
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                        href="/home/<USER>"
                      >
                        Client List
                      </Link>
                    </li>
                  </ul>
                )}
              </div>

              {/* Tutorial Code */}
              <div
                onMouseLeave={handleMouseLeaveFn}
                className="relative "
                data-te-dropdown-ref
                ref={projectDropdownRef}>
                <button
                  className={classNames(
                    "tutorial" === activeItem
                      ? "bg-blue-50 text-blue-700"
                      : "text-gray-700 hover:bg-gray-50",
                    "flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 w-full"
                  )}
                  aria-current={"tutorial" === activeItem ? 'page' : undefined}
                  onClick={() => {
                    handleOpenModal()
                  }}
                  id="dropdownMenuButton1"
                  aria-expanded={isOpenTutorial}
                  ref={projectDropdownRef}
                >
                  Tutorial
                  <InformationCircleIcon className="ml-2 h-5 w-5" />
                </button>
              </div>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}
