import React, { useState, useCallback } from 'react';
import { ArrowUpTrayIcon, DocumentTextIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface FileDropzoneProps {
  onFilesAdded: (files: Array<{
    name: string;
    size: number;
    type: string;
    lastModified: number;
  }>) => void;
  acceptedFileTypes?: string;
  maxFileSizeMB?: number;
  multiple?: boolean;
  disabled?: boolean;
  uploadedFiles?: Array<{
    name: string;
    size: number;
    type: string;
    lastModified: number;
  }>;
  onRemoveFile?: (index: number) => void;
}

const FileDropzone: React.FC<FileDropzoneProps> = ({
  onFilesAdded,
  acceptedFileTypes = '.pdf,.doc,.docx',
  maxFileSizeMB = 10,
  multiple = false,
  disabled = false,
  uploadedFiles = [],
  onRemoveFile
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const validateFiles = useCallback((fileList: FileList | File[]) => {
    const validFiles: File[] = [];
    const maxSizeBytes = maxFileSizeMB * 1024 * 1024;
    const acceptedTypes = acceptedFileTypes.split(',').map(type => type.trim());

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (!acceptedTypes.includes(fileExtension) && !acceptedTypes.includes(file.type)) {
        setErrorMessage(`File type not accepted: ${fileExtension}. Please upload ${acceptedFileTypes} files.`);
        continue;
      }
      
      if (file.size > maxSizeBytes) {
        setErrorMessage(`File too large: ${file.name}. Maximum size is ${maxFileSizeMB}MB.`);
        continue;
      }
      
      validFiles.push(file);
    }
    
    return validFiles;
  }, [acceptedFileTypes, maxFileSizeMB]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    setErrorMessage(null);
    
    if (disabled) return;
    
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    const validFiles = validateFiles(droppedFiles);
    if (validFiles.length > 0) {
      const fileObjects = Array.from(validFiles).map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));
      
      onFilesAdded(fileObjects);
    }
  }, [validateFiles, onFilesAdded, disabled]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setErrorMessage(null);
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const validFiles = validateFiles(selectedFiles);
    if (validFiles.length > 0) {
      const fileObjects = Array.from(validFiles).map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));
      
      onFilesAdded(fileObjects);
    }
    
    // Reset the file input
    e.target.value = "";
  }, [validateFiles, onFilesAdded]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full">
      <div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
          disabled 
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
            : isDragging 
            ? 'border-blue-500 bg-blue-50 scale-[1.02]' 
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
        } ${!disabled ? 'cursor-pointer' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          onChange={handleFileInputChange}
          accept={acceptedFileTypes}
          multiple={multiple}
          disabled={disabled}
        />
        
        <div className="flex flex-col items-center justify-center py-6">
          <div className={`rounded-full p-4 mb-4 transition-all duration-300 ${
            isDragging ? 'bg-blue-100 scale-110' : 'bg-blue-50'
          }`}>
            <ArrowUpTrayIcon className={`h-8 w-8 transition-colors duration-300 ${
              isDragging ? 'text-blue-700' : 'text-blue-600'
            }`} />
          </div>
          
          <p className="text-lg font-semibold text-gray-900 mb-2">
            {isDragging ? 'Drop files here' : 'Drag & drop files here'}
          </p>
          <p className="text-sm text-gray-600 mb-4">
            or click to select files from your computer
          </p>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>Supports {acceptedFileTypes.split(',').join(', ')}</span>
            <span>•</span>
            <span>Max size: {maxFileSizeMB}MB</span>
          </div>
        </div>
      </div>
      
      {errorMessage && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-sm text-red-700">
            <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
            {errorMessage}
          </div>
        </div>
      )}

      {uploadedFiles.length > 0 && (
        <div className="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium text-gray-900">Uploaded Files</h4>
            <span className="text-sm text-gray-500">{uploadedFiles.length} file{uploadedFiles.length !== 1 ? 's' : ''}</span>
          </div>
          
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <DocumentTextIcon className="h-5 w-5 text-blue-600 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                </div>
                {onRemoveFile && (
                  <button 
                    onClick={() => onRemoveFile(index)} 
                    className="text-gray-400 hover:text-red-500 transition-colors p-1"
                    aria-label="Remove file"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileDropzone;
