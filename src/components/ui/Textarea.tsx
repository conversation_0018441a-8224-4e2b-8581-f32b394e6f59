import * as React from "react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

interface TextareaProps extends React.ComponentProps<"textarea"> {
  error?: string;
  label?: string;
  hoverEffect?: boolean;
}

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  TextareaProps
>(({ className, error, label, hoverEffect = false, ...props }, ref) => {
  const [visible, setVisible] = React.useState(false);
  const mouseX = React.useRef(0);
  const mouseY = React.useRef(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!hoverEffect) return;
    const { left, top } = e.currentTarget.getBoundingClientRect();
    mouseX.current = e.clientX - left;
    mouseY.current = e.clientY - top;
  };

  if (hoverEffect) {
    return (
      <div className="w-full">
        {label && (
          <label htmlFor={props.id} className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <motion.div
          className="p-[2px] rounded-lg transition duration-300 group/input"
          onMouseMove={handleMouseMove}
          onMouseEnter={() => setVisible(true)}
          onMouseLeave={() => setVisible(false)}
        >
          <textarea
            className={cn(
              "flex min-h-[100px] w-full border-none bg-gray-50 text-black shadow-input rounded-md px-3 py-2 text-sm",
              "placeholder:text-neutral-400 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400",
              "group-hover/input:shadow-none transition duration-400",
              error && "border-red-300 focus-visible:ring-red-500",
              className
            )}
            ref={ref}
            {...props}
          />
        </motion.div>
        {error && (
          <p className="mt-1 text-xs text-red-600">{error}</p>
        )}
      </div>
    );
  }

  return (
    <div className="w-full">
      {label && (
        <label htmlFor={props.id} className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <textarea
        className={cn(
          "flex min-h-[100px] w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400",
          "focus:outline-none focus:ring-2 focus:ring-blue-500",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "transition-colors duration-200",
          error && "border-red-300 focus:ring-red-500",
          className
        )}
        ref={ref}
        {...props}
      />
      {error && (
        <p className="mt-1 text-xs text-red-600">{error}</p>
      )}
    </div>
  )
})
Textarea.displayName = "Textarea"

export { Textarea }
