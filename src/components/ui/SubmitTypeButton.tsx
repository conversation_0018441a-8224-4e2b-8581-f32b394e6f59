import React from 'react';
import { motion } from 'framer-motion';
import { cn } from "@/lib/utils";

// Define the ButtonProps interface
interface ButtonProps {
  onClick: () => void; // The function to handle the click event
  type?: 'button' | 'submit' | 'reset'; // The type of the button (default is 'submit')
  children: React.ReactNode; // Content inside the button (e.g., text or elements like an icon)
  className?: string; // Optional custom class for additional styling
  variant?: 'primary' | 'secondary' | 'outline' | 'success' | 'danger'; // Button variant
  disabled?: boolean; // Whether the button is disabled
  isLoading?: boolean; // Whether the button is in loading state
  fullWidth?: boolean; // Whether the button should take full width
}

const SubmitTypeButton: React.FC<ButtonProps> = ({
  onClick,
  type = 'submit',
  children,
  className = '',
  variant = 'primary',
  disabled = false,
  isLoading = false,
  fullWidth = false
}) => {
  // Define base styles for different variants based on the styling guide
  const baseStyles = {
    primary: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200",
    secondary: "self-center border-2 border-textSecondary text-textSecondary font-semibold p-2 rounded text-sm hover:bg-textSecondary hover:text-white transition-all duration-300",
    outline: "px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all",
    success: "px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all",
    danger: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-all"
  };

  // Disabled styles
  const disabledStyles = "opacity-70 cursor-not-allowed";

  return (
    <motion.button
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: disabled || isLoading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || isLoading ? 1 : 0.98 }}
      transition={{
        duration: 0.2,
        ease: "easeInOut"
      }}
      className={cn(
        baseStyles[variant],
        disabled && disabledStyles,
        fullWidth ? "w-full" : "w-auto",
        "flex items-center justify-center font-medium",
        className
      )}
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
    >
      {isLoading ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Processing...
        </>
      ) : (
        children
      )}
    </motion.button>
  );
};

export default SubmitTypeButton;
