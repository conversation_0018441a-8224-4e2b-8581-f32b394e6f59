import { useEffect, useMemo, useState } from "react";
import Particles, { initParticlesEngine } from "@tsparticles/react";
import {
  type Container,
  type ISourceOptions,
  MoveDirection,
  OutMode,
} from "@tsparticles/engine";
import { loadSlim } from "@tsparticles/slim"; // Only load slim version

interface ParticlesBackgroundProps {
  id?: string;
  fpsLimit?: number;
  particleCount?: number;
  particleColor?: string;
}

const ParticlesBackground: React.FC<ParticlesBackgroundProps> = ({
  id = "tsparticles",
  fpsLimit = 120,
  particleCount = 90,
  particleColor = "#f2f2f2",
}) => {
  const [init, setInit] = useState(false);

  // Initialize the tsParticles engine once when the component mounts
  useEffect(() => {
    initParticlesEngine(async (engine) => {
      await loadSlim(engine); // Use slim version to reduce bundle size
    }).then(() => {
      setInit(true);
    });
  }, []);

  const particlesLoaded = async (container?: Container): Promise<void> => {
    console.log(container);
  };

  // Set up the particles configuration
  const options: ISourceOptions = useMemo(
    () => ({
      background: {
        color: {
          value: "transparent", // Set background to transparent
        },
      },
      fpsLimit,
      interactivity: {
        events: {
          onClick: {
            enable: true,
            mode: "push",
          },
          onHover: {
            enable: true,
            mode: "repulse",
          },
        },
        modes: {
          push: {
            quantity: 4,
          },
          repulse: {
            distance: 200,
            duration: 0.4,
          },
        },
      },
      particles: {
        color: {
          value: particleColor,
        },
        links: {
          color: particleColor,
          distance: 150,
          enable: true,
          opacity: 0.5,
          width: 1,
        },
        move: {
          direction: MoveDirection.none,
          enable: true,
          outModes: {
            default: OutMode.out,
          },
          random: false,
          speed: 1,
          straight: false,
        },
        number: {
          density: {
            enable: true,
          },
          value: particleCount,
        },
        opacity: {
          value: 0.5,
        },
        shape: {
          type: "circle",
        },
        size: {
          value: { min: 1, max: 5 },
        },
      },
      detectRetina: true,
    }),
    [particleColor, particleCount, fpsLimit],
  );

  // Return particles component if initialized
  if (init) {
    return (
      <Particles
        id={id}
        particlesLoaded={particlesLoaded}
        options={options}
      />
    );
  }

  return null;
};

export default ParticlesBackground;
