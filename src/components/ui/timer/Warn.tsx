

export default function Warn(minutes, seconds, handleLogout, onContinue, messageDetails, type) {
    return (
        <div className='fixed inset-0 h-screen w-screen bg-slate-800 bg-opacity-50 flex justify-center items-center z-50'>
            <div className='w-fit h-fit bg-white rounded-lg flex flex-col justify-center items-center px-4 py-20 mx-4 gap-10 shadow-lg'>
                <div>
                    <span className='text-slate-600 text-4xl'>You will be logged out soon</span>
                </div>
                <div className='w-full h-fit flex flex-col gap-8'>
                    <p className={`text-black w-full text-center font-semibold antialiased ${type == 1 ? "pt-4" : "px-6"}`}>
                        {/* For security, we log you out automatically after 5 mins */}
                        {messageDetails}
                    </p>
                    {type == 1 && <span className='text-black w-full text-center'>
                        {`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`}
                    </span>}
                </div>
                {type == 1
                    ?
                    (<div className='mt-4 flex space-x-4'>
                        <button
                            className='w-[150px] py-[6px] border-[1px] px-4 border-slate-500 bg-white text-black rounded-lg'
                            onClick={handleLogout}
                        >
                            Log out now
                        </button>
                        <button
                            className='w-[150px] py-[6px] px-4 border border-black bg-black text-white rounded-lg'
                            onClick={onContinue}
                        >
                            Continue session
                        </button>
                    </div>)
                    :
                    (<div></div>)}
            </div>
        </div>
    );
}