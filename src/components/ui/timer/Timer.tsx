import React, { useEffect, useState } from 'react';
import { getStartTime, saveStartTime, removeTimer } from '@/api/timer.localStorage';
import Warn from '@/components/ui/timer/Warn';
import { refreshToken } from '@/api/user/userRefreshToken';  // Import the refreshToken function

export default function Timer({ handleLogout, timeDetail }) {
  const [startTime, setStartTime] = useState(() => getStartTime());
  const [isContinue, setIsContinue] = useState(false);
  const [time, setTime] = useState(() => {
    const savedStartTime = getStartTime();
    if (savedStartTime) {
      const elapsed = Math.floor((Date.now() - savedStartTime) / 1000);
      return Math.max(timeDetail - elapsed, 0);
    }
    return timeDetail; // Default to full timeDetail if nothing is saved
  });

  const [isActive, setIsActive] = useState(true);
  const [showModal, setShowModal] = useState(false);

  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [lastMouseMoveTime, setLastMouseMoveTime] = useState(Date.now()); // Track last mouse move time
  const [showWarn, setShowWarn] = useState(false); // Flag to show warning
  const [logoutInProgress, setLogoutInProgress] = useState(false); // Flag to track if the logout countdown has started
  const [logoutTimer, setLogoutTimer] = useState(null); // Store the countdown timer reference

  const hours = Math.floor(time / 3600);
  const minutes = Math.floor((time % 3600) / 60);
  const seconds = time % 60;

  useEffect(() => {
    if (!isActive) return;

    if (!startTime) {
      saveStartTime();
      setStartTime(Date.now());
    }

    const interval = setInterval(() => {
      const elapsedTime = Math.floor((Date.now() - startTime) / 1000);
      const remainingTime = timeDetail - elapsedTime;

      if (remainingTime <= 0) {
        clearInterval(interval);
        removeTimer();
        handleLogout();
        return;
      }

      setTime(remainingTime);

      if (remainingTime <= 300 && !showModal && !isContinue) {
        setShowModal(true);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive, showModal, handleLogout, startTime, timeDetail]);

  // Mouse movement detection
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
      setLastMouseMoveTime(Date.now()); // Update last mouse move time
      setShowWarn(false); // Reset warning when mouse moves

      // Reset logout timer if mouse moves
      if (logoutInProgress) {
        clearTimeout(logoutTimer); // Clear previous logout timer
        setLogoutInProgress(false); // Reset logout state
        setShowWarn(false); // Hide warning
      }
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [logoutInProgress, logoutTimer]);

  // Check inactivity and show warn modal after 4m 50s
  useEffect(() => {
    const checkInactivity = setInterval(() => {
      const inactivityTime = (Date.now() - lastMouseMoveTime) / 1000; // Inactivity time in seconds
      if (inactivityTime >= 290 && !logoutInProgress && !showModal) {
        setShowWarn(true); // Show warning
        setLogoutInProgress(true); // Start the logout countdown

        // Start a 10-second countdown for logout
        const timer = setTimeout(() => {
          handleLogout(); // Call logout after 10 seconds
          removeTimer();
        }, 10000);

        setLogoutTimer(timer); // Store the logout timer

      }
    }, 1000);

    return () => clearInterval(checkInactivity);
  }, [lastMouseMoveTime, logoutInProgress, handleLogout]);

  // Handle continue functionality with refreshToken
  const handleContinue = async () => {
    setIsActive(true);
    setShowModal(false);
    setIsContinue(true);
  
    // Reset the start time to the current time
    const newStartTime = Date.now();
    setStartTime(newStartTime);
    saveStartTime();  // Save the new start time to local storage
  
    // Reset the time remaining (you could calculate this based on timeDetail if needed)
    setTime(timeDetail);
  
    try {
      // Call refreshToken to ensure the session is valid
      const data = await refreshToken();  // Await the refresh token
      if (data.token) {
        console.log("Token refreshed successfully!");
        // You can use the refreshed token for further requests or update state here
      }
    } catch (error) {
      console.error("Error refreshing token:", error);
      // Handle any error during token refresh, possibly triggering a logout
      handleLogout();
    }
  };
  
  return (
    <div className='static'>
      <div className='text-[15px] font-semibold hidden'>
        Time remaining: {`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`}
      </div>
      {showModal && Warn(minutes, seconds, handleLogout, handleContinue, "For security, we log you out automatically after 5 mins", 1)}
      {showWarn && Warn(minutes, seconds, handleLogout, handleContinue, "Inactivity detected, you will be logged out after 10 seconds", 2)}
    </div>
  );
}
