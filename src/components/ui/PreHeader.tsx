"use client"
import React, { useEffect, useState } from 'react';
import Link from "next/link";
import { motion } from 'framer-motion';
import {
  Bars3Icon
} from "@heroicons/react/24/outline";
import { Disclosure } from '@headlessui/react'
import { ChevronUpIcon } from '@heroicons/react/20/solid'

interface PreHeaderButtonProps {
  href: string;
  text: string;
  bgColor: string;
  textColor: string;
}

const HeaderPreLogin = () => {
  return (
    <div className='flex justify-between items-center w-full'>
      <Link href={"/"}>
        <h1 className="text-2xl font-bold text-white">HR-Math</h1>
      </Link>
      <div className='flex gap-8 py-2'>
        <Link
          href="/about"
          className='text-sm font-medium text-gray-200 hover:text-white transition-colors duration-200'>
          Why HR-Math?
        </Link>
        <Link
          href="/aiinterview"
          className='text-sm font-medium text-gray-200 hover:text-white transition-colors duration-200'>
          AI Recruitment
        </Link>
      </div>
      <div className='flex space-x-4 h-full'>
        <Button href="/signup" text="Get Started" bgColor="" textColor="" />
        <Button href="/login" text="Sign In" bgColor="" textColor="" />
      </div>
    </div>
  )
}
const HeaderPreLoginMobile = ({ }) => {
  return (
    <div className='w-full justify-between flex flex-col z-50'>
      <div className="mx-auto w-full max-w-md rounded-lg px-2">
        <Disclosure as="div" className="mt-2">
          {({ open }) => (
            <>
              <Disclosure.Button className="flex h-fit w-fit justify-between rounded-lg text-left text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Bars3Icon className='w-8 h-8 text-gray-200 hover:text-white transition-colors duration-200' />
              </Disclosure.Button>
              <Disclosure.Panel className="px-4 pb-2 pt-4 text-sm text-gray-200">
                <div className="flex flex-col gap-4">
                  <div className='flex flex-col gap-4 py-2'>
                    <Link
                      href="/about"
                      className='text-sm font-medium text-gray-200 hover:text-white transition-colors duration-200'>
                      Why HR-Math?
                    </Link>
                    <Link
                      href="/aiinterview"
                      className='text-sm font-medium text-gray-200 hover:text-white transition-colors duration-200'>
                      AI Recruitment
                    </Link>
                  </div>
                  <div className="space-y-3">
                    <Button href="/signup" text="Get Started" bgColor="" textColor="" />
                    <Button href="/login" text="Sign In" bgColor="" textColor="" />
                  </div>
                </div>
              </Disclosure.Panel>
            </>
          )}
        </Disclosure>
      </div>
    </div>
  )
}

const Button = ({ href, text, bgColor, textColor }: PreHeaderButtonProps) => {
  // Determine if this is a primary or secondary button based on the text
  const isPrimary = text === "Get Started";

  return (
    <Link
      href={href}
      className={`px-4 py-2 rounded-md ${
        isPrimary
          ? "bg-blue-600 hover:bg-blue-700 text-white"
          : "border border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
      } font-medium transition-all duration-200 w-fit lg:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
    >
      {text}
    </Link>
  );
};

const PreHeader = () => {
  const [windowWidth, setWindowWidth] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Set initial width and mark component as mounted
    setWindowWidth(window.innerWidth);
    setIsMounted(true);

    // Function to update the window width state
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Add event listener for resize
    window.addEventListener('resize', handleResize);

    // Clean up the event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []); // Empty dependency array to only run on mount and unmount

  // Return a placeholder during server-side rendering
  if (!isMounted) {
    return (
      <header className="h-fit w-full py-4 fixed flex justify-between items-center top-0 left-0 right-0 z-50 shadow-lg backdrop-blur-md lg:backdrop-blur-sm mx-auto max-w-8xl px-2 lg:px-8">
        <div className="w-full">
          {/* Placeholder content that matches the structure but doesn't depend on client-side state */}
          <div className="invisible">Loading...</div>
        </div>
      </header>
    );
  }

  return (
    <header className="h-fit w-full py-4 fixed flex justify-between items-center top-0 left-0 right-0 z-50 shadow-lg backdrop-blur-md lg:backdrop-blur-sm mx-auto max-w-8xl px-2 lg:px-8">
      {windowWidth >= 800 ? <HeaderPreLogin /> : <HeaderPreLoginMobile />}
    </header>
  );
};

export default PreHeader;
