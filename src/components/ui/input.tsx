// Input component extends from shadcnui - https://ui.shadcn.com/docs/components/input
"use client";
import * as React from "react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { componentPatterns } from '@/lib/design-tokens';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  icon?: React.ReactNode;
  hoverEffect?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, label, icon, hoverEffect = false, ...props }, ref) => {
    const [visible, setVisible] = React.useState(false);
    const mouseX = React.useRef(0);
    const mouseY = React.useRef(0);

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
      if (!hoverEffect) return;
      const { left, top } = e.currentTarget.getBoundingClientRect();
      mouseX.current = e.clientX - left;
      mouseY.current = e.clientY - top;
    };

    if (hoverEffect) {
      return (
        <div className="w-full">
          {label && (
            <label htmlFor={props.id} className="block text-sm font-medium text-gray-700 mb-1">
              {label}
            </label>
          )}
          <motion.div
            className="p-[2px] rounded-lg transition duration-300 group/input"
            onMouseMove={handleMouseMove}
            onMouseEnter={() => setVisible(true)}
            onMouseLeave={() => setVisible(false)}
          >
            <input
              type={type}
              className={cn(
                componentPatterns.input.base,
                "border-none bg-gray-50 shadow-input",
                "group-hover/input:shadow-none transition duration-400",
                error && componentPatterns.input.error,
                icon && "pl-10",
                className
              )}
              ref={ref}
              {...props}
            />
          </motion.div>
          {error && (
            <p className="mt-1 text-xs text-error-600">{error}</p>
          )}
        </div>
      );
    }

    return (
      <div className="w-full">
        {label && (
          <label htmlFor={props.id} className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <div className={cn("relative", icon && "flex items-center")}>
          {icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              {icon}
            </div>
          )}
          <input
            type={type}
            className={cn(
              componentPatterns.input.base,
              error && componentPatterns.input.error,
              icon && "pl-10",
              className
            )}
            ref={ref}
            {...props}
          />
        </div>
        {error && (
          <p className="mt-1 text-xs text-error-600">{error}</p>
        )}
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };
