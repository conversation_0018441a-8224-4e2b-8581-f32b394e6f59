import React, { useEffect } from 'react';
import { XMarkIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface SubmitModalProps {
  modalName?: string;
  modalText?: string;
  type?: number;
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  landonpage?: string; 
}

export default function SubmitModal({ modalName, modalText, onClose ,landonpage}: SubmitModalProps) {
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      if (landonpage) {
        router.push(landonpage); 
      } else {
        onClose();
      }
    }, 4000);

    return () => clearTimeout(timer);
  }, [landonpage, onClose, router]);


  return (
    <div className="modal flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
      <div className="flex flex-col  justify-center items-center gap-5 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] h-[300px] relative">
        <button className="absolute top-2 right-2" onClick={onClose}>
          <XMarkIcon className="h-5 w-5 text-gray-500" />
        </button>
        <div className="flex flex-col gap-3 justify-center items-center text-center">
          <CheckCircleIcon className='text-green-500 h-10 w-10' />
          <h1 className="text-xl font-bold">{modalName}</h1>
          <p className="text-gray-700">{modalText}</p>
        </div>
        <div className="flex justify-center">
                            {landonpage ? (
              <Link href={landonpage}>
                <button
                                type="button"
                              
                                className="bg-secondary text-white rounded-md px-4 py-2 shadow-md"
                            >
                                OK
                            </button>
              </Link>
            ) : null}
                        </div>
      </div>
    </div>
  );
}
