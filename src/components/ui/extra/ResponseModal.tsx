import React from 'react';
import { CheckBadgeIcon, XCircleIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface ResponseModalProps {
    type: 'err' | 'msg';  // Type can either be 'err' for error or 'msg' for message
    message: string;      // The message to display in the modal
    onClose: () => void;  // The function to call when the modal should close
}

const ResponseModal: React.FC<ResponseModalProps> = ({ type, message, onClose }) => {
    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm">
            <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className={`w-full max-w-md bg-white rounded-lg shadow-xl p-6 relative`}
                onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside the modal
            >
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        {type === 'err' ? 'Error' : 'Success'}
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full p-1"
                    >
                        <XMarkIcon className="h-5 w-5" />
                    </button>
                </div>

                {/* Content */}
                <div className="flex flex-col items-center">
                    {type === "err" ? (
                        <>
                            <div className="flex justify-center items-center mb-4 bg-red-100 p-3 rounded-full">
                                <XCircleIcon className="h-10 w-10 text-red-500" />
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-700 mb-4">{message}</p>
                                <button
                                    onClick={onClose}
                                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200"
                                >
                                    Close
                                </button>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="flex justify-center items-center mb-4 bg-green-100 p-3 rounded-full">
                                <CheckBadgeIcon className="h-10 w-10 text-green-500" />
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-700 mb-4">{message}</p>
                                <button
                                    onClick={onClose}
                                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-200"
                                >
                                    OK
                                </button>
                            </div>
                        </>
                    )}
                </div>
            </motion.div>
        </div>
    );
};

export default ResponseModal;
