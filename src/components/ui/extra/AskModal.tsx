import React from 'react';
import { motion } from 'framer-motion';
import { useRef } from "react"

const AskModal = ({ questionToAsk, questionDetail, onYes, onNo, closeModal }) => {

    const constraints = {
        width: window.innerWidth,
        height: window.innerHeight,
        backgroundColor: "var(--hue-1-transparent)",
        borderRadius: 10,
    }

    const constraintsRef = useRef<HTMLDivElement>(null);

    return (
        <motion.div
            className="w-full h-full bg-gray-800/50 backdrop-blur-sm flex justify-center items-center fixed bottom-4 right-4 z-50"
            ref={constraintsRef} style={constraints}
        >
            <motion.div
                drag
                dragConstraints={constraintsRef}
                dragElastic={0.2}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0 }}
                transition={{ duration: 0.4, type: 'spring', bounce: 0.5 }}
                className='w-[300px] h-[150px] rounded-xl bg-black/80 p-6 absolute bottom-0 lg:right-0 z-40'
            >
                <h1 className="text-white text-lg font-semibold">{questionToAsk}</h1>
                <p className="text-white text-sm">{questionDetail}</p>
                <div className="flex justify-around mt-4 w-full gap-10">
                    <button
                        className="px-4 py-2 bg-green-500 text-white rounded w-full hover:bg-green-600"
                        onClick={() => {
                            onYes();
                            closeModal();
                        }}
                    >
                        Yes
                    </button>
                    <button
                        className="px-4 py-2 bg-red-500 text-white rounded w-full hover:bg-red-600"
                        onClick={() => {
                            onNo();
                            closeModal();
                        }}
                    >
                        No
                    </button>
                </div>
            </motion.div>
        </motion.div >
    );
};

export default AskModal;
