import React from 'react';
import { XMarkIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface PDFTutorialModalProps {
    show: boolean;
    pdfUrl: string;
    onClose: () => void;
}

const PDFTutorialModal: React.FC<PDFTutorialModalProps> = ({ show, pdfUrl, onClose }) => {
    if (!show) return null; // Don't render modal if 'show' is false

    const pdfUrl2 = "/tutorials/HRMath.pdf"; // Correct path to access files in the public folder

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm">
            <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="w-full max-w-6xl bg-white rounded-lg shadow-xl relative max-h-[80vh] overflow-hidden"
            >
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Tutorial</h3>
                    <button
                        className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full p-1"
                        onClick={onClose}
                    >
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </div>

                {/* Content */}
                <div className="p-4">
                    <iframe
                        src={pdfUrl2} // Use the corrected path
                        width="100%"
                        height="600px"
                        title="PDF Viewer"
                        className="border-none rounded"
                    />
                </div>
            </motion.div>
        </div>
    );
};

export default PDFTutorialModal;
