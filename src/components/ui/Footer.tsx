import React from 'react';
import { Spotlight } from './Spotlight';

interface FooterProps {
    bgColorAccept?: boolean;
}

const Footer = ({ }: FooterProps) => {
    return (
        <footer className="w-full bg-white border-t border-gray-200 pt-12 px-4 lg:px-10">
            <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                    {/* Sitemap Section */}
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Sitemap</h3>
                        <ul className="space-y-3">
                            <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200">Home</a></li>
                            <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200">About Us</a></li>
                            <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200">Services</a></li>
                            <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200">Blog</a></li>
                            <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200">Contact</a></li>
                        </ul>
                    </div>

                    {/* Services Section */}
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Services</h3>
                        <ul className="space-y-3">
                            <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200">AI Interview</a></li>
                            <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200">Deep Screening</a></li>
                        </ul>
                    </div>

                    {/* Contact Section */}
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Us</h3>
                        <ul className="space-y-3">
                            <li className="text-gray-600">123 Business Rd, Suite 456</li>
                            <li className="text-gray-600">New York, NY 10001</li>
                            <li className="text-gray-600">Phone: +****************</li>
                            <li className="text-gray-600">Email: <EMAIL></li>
                        </ul>
                    </div>

                    {/* Social Media Section */}
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Follow Us</h3>
                        <div className="flex space-x-4">
                            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 p-2 rounded-full hover:bg-gray-100">
                                <i className="fab fa-linkedin-in text-xl"></i>
                            </a>
                            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 p-2 rounded-full hover:bg-gray-100">
                                <i className="fab fa-twitter text-xl"></i>
                            </a>
                            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 p-2 rounded-full hover:bg-gray-100">
                                <i className="fab fa-facebook-f text-xl"></i>
                            </a>
                            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors duration-200 p-2 rounded-full hover:bg-gray-100">
                                <i className="fab fa-instagram text-xl"></i>
                            </a>
                        </div>
                    </div>
                </div>

                {/* Copyright Section */}
                <div className="border-t border-gray-200 py-6 flex flex-col md:flex-row md:justify-between">
                    <p className="text-sm text-center text-gray-600 font-normal">
                        © HR-Math {new Date().getFullYear()}. All rights reserved. Powered by Dono Consulting
                    </p>
                    <div className="flex gap-6 justify-center mt-4 md:mt-0">
                        <a href="#" className="text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200">Privacy Policy</a>
                        <a href="#" className="text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200">Terms and Conditions</a>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
