import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";

export const HoverEffect = ({
  items,
  className,
  cardVariant = "basic",
  columns = 3,
}: {
  items: {
    title: string;
    description: string;
    link: string;
    badge?: string;
  }[];
  className?: string;
  cardVariant?: "basic" | "border" | "topBar" | "header" | "selected" | "badge";
  columns?: 1 | 2 | 3 | 4;
}) => {
  let [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // Define grid columns based on the columns prop
  const gridColumns = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
  };

  return (
    <div
      className={cn(
        `grid ${gridColumns[columns]} gap-6`,
        className
      )}
    >
      {items.map((item, idx) => (
        <Link
          href={item?.link}
          key={item?.link}
          className="block h-full w-full"
          onMouseEnter={() => setHoveredIndex(idx)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <div>
            {cardVariant === "badge" && item.badge ? (
              <Card variant={cardVariant}>
                {item.badge}
                <CardTitle>{item.title}</CardTitle>
                <CardDescription>{item.description}</CardDescription>
              </Card>
            ) : (
              <Card variant={cardVariant}>
                <CardTitle>{item.title}</CardTitle>
                <CardDescription>{item.description}</CardDescription>
              </Card>
            )}
          </div>
        </Link>
      ))}
    </div>
  );
};

export const Card = ({
  className,
  children,
  variant = "basic",
}: {
  className?: string;
  children: React.ReactNode;
  variant?: "basic" | "border" | "topBar" | "header" | "selected" | "badge";
}) => {
  // Define different card variants based on the styling guide
  const cardVariants = {
    basic: "bg-white rounded-lg shadow-sm p-4",
    border: "bg-white rounded-lg border border-gray-200 p-4",
    topBar: "bg-white rounded-xl border border-gray-200 flex flex-col h-full",
    header: "bg-white rounded-lg overflow-hidden shadow-sm",
    selected: "bg-white rounded-lg border border-blue-400 shadow-sm ring-2 ring-blue-200 p-4",
    badge: "bg-white rounded-lg shadow-sm p-4 relative"
  };

  if (variant === "topBar") {
    return (
      <div className={cn(cardVariants[variant], className)}>
        <div className="h-2 w-full bg-blue-500 rounded-t-xl"></div>
        <div className="p-4">
          {children}
        </div>
      </div>
    );
  }

  if (variant === "header") {
    // Assuming the first child is the header content and the rest is the body
    const [header, ...rest] = React.Children.toArray(children);
    return (
      <div className={cn(cardVariants[variant], className)}>
        <div className="bg-blue-100 h-32 w-full relative">
          <div className="absolute inset-0 flex items-center justify-center">
            {header}
          </div>
        </div>
        <div className="p-4">
          {rest}
        </div>
      </div>
    );
  }

  if (variant === "badge") {
    // Assuming the first child is the badge and the rest is the content
    const [badge, ...rest] = React.Children.toArray(children);
    return (
      <div className={cn(cardVariants[variant], className)}>
        <span className="absolute top-3 left-3 bg-green-100 rounded-full px-2 py-0.5 text-xs font-medium text-green-800">
          {badge}
        </span>
        <div className="mt-6">
          {rest}
        </div>
      </div>
    );
  }

  return (
    <div className={cn(cardVariants[variant], className)}>
      {children}
    </div>
  );
};
export const CardTitle = ({
  className,
  children,
  variant = "default",
}: {
  className?: string;
  children: React.ReactNode;
  variant?: "default" | "section" | "page";
}) => {
  const titleVariants = {
    default: "text-lg font-medium text-gray-800 mb-2",
    section: "text-lg font-semibold text-gray-800 mb-3",
    page: "text-xl sm:text-2xl font-bold text-gray-900 mb-4"
  };

  return (
    <h3 className={cn(titleVariants[variant], className)}>
      {children}
    </h3>
  );
};

export const CardDescription = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <p
      className={cn(
        "text-sm text-gray-600 leading-relaxed",
        className
      )}
    >
      {children}
    </p>
  );
};
