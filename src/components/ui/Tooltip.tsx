import React from 'react';

const Tooltip = ({ text, children }) => {
  return (
    <div className="relative group">
      {/* The target element (children) */}
      {children}

      {/* The tooltip */}
      <div className="absolute bottom-0 right-0 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-sm rounded-md w-[250px]">
        {text}
      </div>
    </div>
  );
};

export default Tooltip;
