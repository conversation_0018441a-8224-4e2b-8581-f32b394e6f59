import React from "react";
import { cn } from "@/lib/utils";

// Define an interface for the component props
interface HeadingProps {
  pgHeading: string;
  textColor?: string;
  level?: 'h1' | 'h2' | 'h3' | 'h4';
  className?: string;
  variant?: 'default' | 'section' | 'page' | 'subtitle';
}

export default function Heading({
  pgHeading,
  textColor = "text-gray-900",
  level = 'h2',
  className,
  variant = 'default'
}: HeadingProps) {
  // Define heading variants based on the styling guide
  const headingVariants = {
    default: {
      h1: "text-3xl font-bold mb-6",
      h2: "text-2xl font-semibold mb-4",
      h3: "text-xl font-medium mb-3",
      h4: "text-lg font-medium mb-2"
    },
    section: {
      h1: "text-2xl font-bold mb-4 border-b pb-2 border-gray-200",
      h2: "text-xl font-semibold mb-3 border-b pb-2 border-gray-200",
      h3: "text-lg font-medium mb-2 border-b pb-2 border-gray-200",
      h4: "text-base font-medium mb-2 border-b pb-2 border-gray-200"
    },
    page: {
      h1: "text-4xl font-extrabold mb-8 tracking-tight",
      h2: "text-3xl font-bold mb-6 tracking-tight",
      h3: "text-2xl font-bold mb-4",
      h4: "text-xl font-semibold mb-3"
    },
    subtitle: {
      h1: "text-xl font-normal text-gray-600 mb-6",
      h2: "text-lg font-normal text-gray-600 mb-4",
      h3: "text-base font-normal text-gray-600 mb-3",
      h4: "text-sm font-normal text-gray-600 mb-2"
    }
  };

  const Tag = level;

  return (
    <Tag className={cn(headingVariants[variant][level], textColor, className)}>
      {pgHeading}
    </Tag>
  );
}