import Link from 'next/link';
import React from 'react';
import Image from 'next/image';
import ParticlesBackground from '../ui/Design';
import SubmitTypeButton from "@/components/ui/SubmitTypeButton"

interface LoginComponentProps {
  email: string;
  setEmail: React.Dispatch<React.SetStateAction<string>>;
  password: string;
  setPassword: React.Dispatch<React.SetStateAction<string>>;
  handleUserNavigation: () => void;
  handleSignIn: () => void;
  errorMessage?: string;
}

const LoginComponent: React.FC<LoginComponentProps> = ({
  email,
  setEmail,
  password,
  setPassword,
  handleUserNavigation,
  handleSignIn,
  errorMessage
}) => {
  return (
    <div className="w-10/12 lg:w-[900px] bg-white h-fit flex flex-col lg:flex-row shadow-2xl rounded-2xl p-8 gap-10 justify-between items-center"
      style={{ backgroundImage: `url(/login/login_background.svg)` }}>

      {/* form */}
      <div className='h-full w-full flex-1 p-4'>
        <h2 className="mb-10 text-center text-2xl font-bold leading-9 tracking-tight text-textPrimary">
          Sign in to your account
        </h2>
        <div className="space-y-6">
          {/* Email Input */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium leading-6 text-textColor">
              Email address
            </label>
            <div className="mt-2">
              <input
                id="email"
                name="email"
                type="email"
                placeholder="Enter Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                autoComplete="email"
                required
                className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              />
            </div>
          </div>

          {/* Password Input */}
          <div>
            <div className="flex items-center justify-between">
              <label htmlFor="password" className="block text-sm font-medium leading-6 text-textColor">
                Password
              </label>
              <div className="text-sm">
                <a
                  href="#"
                  onClick={handleUserNavigation}
                  className="font-semibold bg-gradient-to-t from-violet-900 to-indigo-600 bg-clip-text text-transparent"
                >
                  Forgot password?
                </a>
              </div>
            </div>
            <div className="mt-2">
              <input
                id="password"
                name="password"
                type="password"
                placeholder="Enter password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                autoComplete="current-password"
                required
                className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              />
            </div>
          </div>

          {/* Error Message */}
          {errorMessage && (
            <div className="flex justify-center text-sm text-red-500">
              {errorMessage}
            </div>
          )}

          {/* Sign In Button */}
          <div>
            <SubmitTypeButton onClick={handleSignIn}>
              Sign in &rarr;
            </SubmitTypeButton>

          </div>
        </div>

        {/* Registration Link */}
        <p className="mt-10 text-center text-sm text-gray-500">
          Not a member?{" "}
          <Link
            href="/signup"
            className="font-semibold leading-6 bg-gradient-to-t from-violet-900 to-indigo-600 bg-clip-text text-transparent"
          >
            Let&apos;s get started!
          </Link>
        </p>
      </div>

      {/* Border */}
      <div className='h-[95%] border' />

      {/* Image */}
      <div className='h-full w-full flex-1'>
        <Image src="/login/login.svg" alt="login" width={500} height={500} />
      </div>
    </div>
  );
};

export default LoginComponent;
