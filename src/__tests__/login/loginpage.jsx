import { render, screen } from "@testing-library/react";
import '@testing-library/jest-dom'; // Correctly import jest-dom
import LoginPage from "../../app/login/page"
import React from 'react';
import { it } from "node:test";

it("test the Img of login page", () => {
  render(<LoginPage />);
  const imgAlt = screen.getByAltText("Skilling.ai");
  expect(imgAlt).toBeInTheDocument();
});ayarn 

it("test the heading of login page", () => {
  render(<LoginPage />);
  const loginHeadings = screen.getByText(/Sign in to your account/i);
  expect(loginHeadings).toBeInTheDocument();
});

it("test the email input of login page", () => {
  render(<LoginPage />);
  const emailInput = screen.getByPlaceholderText("Enter Email");
  expect(emailInput).toBeInTheDocument();
});

it("test the password input of login page", () => {
  render(<LoginPage />); 
  const passwordInput = screen.getByPlaceholderText("Enter Password");
  expect(passwordInput).toBeInTheDocument();
});

it("test the button of home page" ,()=>{
  render(<LoginPage />); 
  const buttontitle = screen.getByTitle("login btn");
  expect(buttontitle).toBeInTheDocument();
})