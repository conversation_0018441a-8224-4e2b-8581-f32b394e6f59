import { postProject } from '@/api/home/<USER>/postProject';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function usePostProject(client_id: number | null, project_name: string, description: string) {
    const queryClient = useQueryClient();

    // Return early if client_id is null
    if (client_id === null || NaN) {
        return {
            mutate: () => {
                console.log('Mutation not executed because client_id is null');
            }
        };
    }

    return useMutation(() => postProject(client_id, project_name, description), {
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['postproject'] });
            console.log('project created/posted successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
