import { removeUploadedCV } from '@/api/home/<USER>/cv/removeUploadedCV';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function useRemoveUploadedCV(run_id: number) {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: () => removeUploadedCV(run_id),
        onSuccess: (data) => {
            console.log('CV deleted successfully:', data);
        },
        onError: (error: unknown) => {
            
        },
        onSettled: (data, error) => {
            // Handle completion, whether success or failure
            console.log('Mutation settled:', { data, error });
        },
    });
}
