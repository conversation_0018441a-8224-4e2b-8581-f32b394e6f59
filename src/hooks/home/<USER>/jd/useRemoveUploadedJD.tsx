import { removeUploadedJD } from '@/api/home/<USER>/jd/removeUploadedJD';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function useRemoveUploadedJD(run_id: number) {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: () => removeUploadedJD(run_id),
        onSuccess: (data) => {
            console.log('CV deleted successfully:', data);
        },
        onError: (error: unknown) => {
            
        },
        onSettled: (data, error) => {
            // Handle completion, whether success or failure
            console.log('Mutation settled:', { data, error });
        },
    });
}
