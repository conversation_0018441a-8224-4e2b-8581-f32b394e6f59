
import { generateReport } from '@/api/home/<USER>/generateReport';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function useGenerateReport(run_id: Number, default_weight: boolean,weight_sim:Number,weight_con:Number ,threshold_value:Number,more_info:String) {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn:() => generateReport(run_id, default_weight,weight_sim,weight_con,(threshold_value ? threshold_value : 1) ,more_info), 
   
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['postExtraInfo'] });
            console.log('Extra data created/posted successfully:', data);
        },
        onError: (error) => {
             // Handle error, log or display a message
             if (error instanceof Error) {
                console.error('Error uploading CV:', error.message);
            } else {
                console.error('Unknown error occurred during CV upload');
            }
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}