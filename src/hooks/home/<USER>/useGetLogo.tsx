import { getLogo } from '@/api/home/<USER>/getLogo';
import { useQuery } from '@tanstack/react-query';

export function useGetLogo(fileName: string) {
  if (!fileName) {
    return { data: null, isLoading: false, isError: false }; // Return default values if fileName is falsy
  }

  console.log("clientInfo4 fileName", fileName);

  // Query the logo when fileName is provided
  return useQuery(['useGetLogo', fileName], () => getLogo(fileName), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1,
  });
}
