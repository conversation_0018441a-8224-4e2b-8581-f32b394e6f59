import { useQuery } from '@tanstack/react-query';
import { getDownloadProfileReport } from '@/api/home/<USER>/getDownloadProfileReport';

export function useGetDownloadProfileReport(run_id: number | null, file_type: number) {
  return useQuery(
    ['GetDownloadProfileReport', run_id, file_type],
    () => (run_id ? getDownloadProfileReport(run_id, file_type) : Promise.resolve(null)), // Ensure the query only runs when run_id is available
    {
      enabled: !!run_id, // Only enable the query when run_id is available
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
    }
  );
}
