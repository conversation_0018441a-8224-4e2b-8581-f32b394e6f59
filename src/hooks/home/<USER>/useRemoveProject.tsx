import { useMutation, useQueryClient } from '@tanstack/react-query';
import { removeProject } from '@/api/home/<USER>/removeProject';

export function useRemoveProject(project_id: number) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => removeProject(project_id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['GetAllProject'] });
      queryClient.invalidateQueries({ queryKey: ['getProjectsWithClientId'] });
      queryClient.invalidateQueries({ queryKey: ['getProjectsWithUserId'] });
      console.log('Extra data created/posted successfully:', data);
    },
    onError: (error) => {
      // Handle error, log or display a message
      if (error instanceof Error) {
        console.error('Error uploading CV:', error.message);
      } else {
        console.error('Unknown error occurred during CV upload');
      }
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
