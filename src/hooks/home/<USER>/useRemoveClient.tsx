import { useMutation, useQueryClient } from '@tanstack/react-query';
import { removeClient } from '@/api/home/<USER>/removeClient';

export function useRemoveClient(client_id: number) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => removeClient(client_id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['getAllClientsWithUserId'] });
      console.log('Extra data created/posted successfully:', data); 
    },
    onError: (error) => {
      // Handle error, log or display a message
      if (error instanceof Error) {
        console.error('Error uploading CV:', error.message);
      } else {
        console.error('Unknown error occurred during CV upload');
      }
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
