import { postUploadJD } from '@/api/home/<USER>/jd/postUploadJD';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function usePostUploadJD(run_id: number, file: File) {
    const queryClient = useQueryClient();
    console.log("fileuploadjd use",file)
    return useMutation({
        mutationFn: () => postUploadJD(run_id, file),
        onSuccess: (data) => {
            // Invalidate queries related to 'UploadJD' to refresh data
            queryClient.invalidateQueries({ queryKey: ['UploadJD'] });
            console.log('JD uploaded successfully:', data);
        },
        onError: (error: unknown) => {
            // Handle error, log or display a message
            if (error instanceof Error) {
                console.error('Error uploading JD:', error.message);
            } else {
                console.error('Unknown error occurred during JD upload');
            }
        },
        onSettled: (data, error) => {
            // Handle completion, whether success or failure
            console.log('Mutation settled:', { data, error });
        },
    });
}
