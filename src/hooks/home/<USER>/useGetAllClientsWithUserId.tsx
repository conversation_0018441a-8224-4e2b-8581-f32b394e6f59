"use client"
import { getAllClientsWithUserId } from '@/api/home/<USER>/getAllClientsWithUserId';
import { useQuery } from '@tanstack/react-query';

export function useGetAllClientsWithUserId(user_id: number) {
  return useQuery(['getAllClientsWithUserId', user_id], () => getAllClientsWithUserId(user_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}
