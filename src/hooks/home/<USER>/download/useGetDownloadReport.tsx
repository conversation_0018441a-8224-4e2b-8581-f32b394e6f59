import { useQuery } from '@tanstack/react-query';
import { getDownloadReport } from '@/api/home/<USER>/download/getDownloadReport';

export function useGetDownloadReport(run_id: number | null, file_type: number) {
  return useQuery(
    ['getDownloadReport', run_id, file_type],
    () => (run_id ? getDownloadReport(run_id, file_type) : Promise.resolve(null)), // Ensure the query only runs when run_id is available
    {
      enabled: !!run_id, // Only enable the query when run_id is available
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      onError: (error) => {
        console.error('Error fetching download report:', error);
      },
      onSettled: () => {
        // This will be called after the query either succeeds or fails.
        // Useful if you want to perform any cleanup or logging.
      }
    }
  );
}
