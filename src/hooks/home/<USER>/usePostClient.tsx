
import { postClient } from '@/api/home/<USER>/postClient';
import { Client } from '@/types/LMSTypes';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function usePostClient(client_name: string, client_description: string, photo: Client) {
    const queryClient = useQueryClient();

    return useMutation(() => postClient(client_name, client_description, photo), {
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['postclient'] });
            console.log('client created/posted successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
