import { postUploadCV } from '@/api/home/<USER>/cv/postUploadCV';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function usePostUploadCV(run_id: number, file: File) {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: () => postUploadCV(run_id, file),
        onSuccess: (data) => {
            // Invalidate queries related to 'UploadCV' to refresh data
            queryClient.invalidateQueries({ queryKey: ['UploadCV'] });
            console.log('CV uploaded successfully:', data);
        },
        onError: (error: unknown) => {
            // Handle error, log or display a message
            if (error instanceof Error) {
                console.error('Error uploading CV:', error.message);
            } else {
                console.error('Unknown error occurred during CV upload');
            }
        },
        onSettled: (data, error) => {
            // Handle completion, whether success or failure
            console.log('Mutation settled:', { data, error });
        },
    });
}
