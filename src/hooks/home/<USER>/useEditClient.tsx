
import { editClient } from '@/api/home/<USER>/editClient';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

// interface EditClient {
//     client_id: number;
//     client_name: string;
//     client_description: string
//     file: File;
// }

export function useEditClient(client_id: string, client_name: string, client_description: string, file: File) {
    const queryClient = useQueryClient();

    
    console.log("filessssss213",file)

    return useMutation(() => editClient(client_id, client_name, client_description, file), {
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['editclient'] });
            console.log('client edited successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
