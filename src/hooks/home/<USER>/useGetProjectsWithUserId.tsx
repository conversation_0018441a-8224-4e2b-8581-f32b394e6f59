import { getProjectsWithUserId } from '@/api/home/<USER>/getProjectsWithUserId';
import { useQuery } from '@tanstack/react-query';

export function useGetProjectsWithUserId(user_d: number) {
  return useQuery(['getProjectsWithUserId', user_d], () => getProjectsWithUserId(user_d), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}
