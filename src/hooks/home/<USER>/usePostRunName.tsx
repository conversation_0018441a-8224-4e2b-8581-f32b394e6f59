
import { postRunName } from '@/api/home/<USER>/postRunName';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function usePostRunName(project_id: number, run_name: string) {
    const queryClient = useQueryClient();

    return useMutation(() => postRunName(project_id, run_name), {
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['postrunname'] });
            console.log(' Run Name posted successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
