import { getProjectsWithClientId } from '@/api/home/<USER>/getProjectsWithClientId';
import { useQuery } from '@tanstack/react-query';

export function useGetProjectsWithClientId(client_id: string | null) {
  if (client_id === "No") {
    return useQuery(['getProjectsWithClientId', client_id], () => Promise.resolve([]), {
      enabled: false,  // Don't fetch anything if the client_id is "No"
    });
  } else {
    return useQuery(
      ['getProjectsWithClientId', client_id], 
      () => getProjectsWithClientId(parseInt(client_id)), 
      {
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        refetchOnReconnect: false,
        staleTime: 1000,  // Adjust this as needed
        enabled: client_id !== null && client_id !== "No"  // Prevent query if client_id is "No"
      }
    );
  }
}
