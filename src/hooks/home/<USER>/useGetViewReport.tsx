import { getViewReport } from '@/api/home/<USER>/getViewReport';
import { useQuery } from '@tanstack/react-query';


export function useGetViewReport(run_id:number,file_type:Number) {
  return useQuery(['useGetViewReport', run_id,file_type], () => getViewReport(run_id,file_type), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}
