import { forgetPassword } from "@/api/user/forgetPassword";
import { useMutation } from '@tanstack/react-query';

export function useForgetPassword(email: string) {
    return useMutation(() => forgetPassword(email), {
        onSuccess: (data) => {
            console.log('Reset password success:', data);
        },
        onError: (error) => {
            console.error('Reset password failed:', error);
        },
    });
}
