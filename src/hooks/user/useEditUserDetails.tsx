
import { editUserDetails } from '@/api/user/editUserDetails';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

// interface EditClient {
//     client_id: number;
//     client_name: string;
//     client_description: string
//     file: File;
// }

export function useEditUserDetails(user_id: number, email: string, password: string, phone: number, user_full_name: string) {
    const queryClient = useQueryClient();

    return useMutation(() => editUserDetails(user_id, email, password, phone, user_full_name), {
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['GetUserDetailsWithLogo'] });
            console.log('client edited successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
