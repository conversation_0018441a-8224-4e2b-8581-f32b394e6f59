
import { editUserLogo } from '@/api/user/editUserLogo';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

// interface EditClient {
//     client_id: number;
//     client_name: string;
//     client_description: string
//     file: File;
// }

export function useEditUserLogo(user_id: number, file: File) {
    const queryClient = useQueryClient();

    return useMutation(() => editUserLogo(user_id, file), {
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['GetUserDetailsWithLogo'] });
            console.log('client edited successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
