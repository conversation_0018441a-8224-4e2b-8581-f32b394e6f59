import { resetPassword } from '@/api/user/resetPassword';
import { useMutation } from '@tanstack/react-query';

export function useResetPassword(password: string, token: string) {
    return useMutation(() => resetPassword(password, token), {
        onSuccess: (data) => {
            console.log('Reset password success:', data);
        },
        onError: (error) => {
            console.error('Reset password failed:', error);
        },
    });
}
