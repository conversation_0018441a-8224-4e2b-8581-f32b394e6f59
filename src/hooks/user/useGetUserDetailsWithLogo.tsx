import { getUserDetailsWithLogo } from '@/api/user/getUserDetailsWithLogo';
import { useQuery } from '@tanstack/react-query';

export function useGetUserDetailsWithLogo(user_id: number) {
  return useQuery(['GetUserDetailsWithLogo', user_id], () => getUserDetailsWithLogo(user_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}
